import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/widget/customConfirmationDialog.dart';
import 'package:seawork/screens/employee/calendar/components/calendarTimeUtils.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/employee/models/meeting.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/screens/employee/calender/offlineMeetBottomsheet.dart';
import 'package:seawork/screens/employee/employee/providers/meetingProvider.dart';
import 'package:seawork/utils/util.dart';

class MeetingDetailsScreen extends ConsumerStatefulWidget {
  const MeetingDetailsScreen({
    Key? key,
    required this.meeting,
  }) : super(key: key);

  final Meeting meeting;

  @override
  _MeetingDetailsScreenState createState() => _MeetingDetailsScreenState();
}

class _MeetingDetailsScreenState extends ConsumerState<MeetingDetailsScreen> {
  final int _selectedIndex = 1;

  @override
  void initState() {
    super.initState();
    // Initialize timezone database
    TimeUtils.initialize();
  }

  @override
  Widget build(BuildContext context) {
    // Parse the meeting date from the meeting object
    final meetingDate = DateTime.parse(widget.meeting.date);
    final date = DateFormat('EEE MMMM d, yyyy').format(meetingDate);
    final startTime = TimeUtils.formatTimeForDisplay(
      widget.meeting.time,
      meetingDate,
    );
    final endTimeDateTime = TimeUtils.calculateEndTime(
      widget.meeting.time,
      widget.meeting.durationMinutes,
      meetingDate,
    );
    final endTime = DateFormat('h:mm a').format(endTimeDateTime).toLowerCase();
    final timeRange = '$startTime - $endTime';

    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(
        title: 'Details',
        showActionIcon: true,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Meeting title from the meeting object
                      DmSansText(
                        widget.meeting.title,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.blackColor,
                      ),
                      if (widget.meeting.isCancelled)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.lightRedShade,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: OpenSansText(
                              'Cancelled',
                              fontSize: 12,
                              color: AppColors.redColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      const SizedBox(height: 12),

                      // Date and time section
                      Row(
                        children: [
                          CustomSvgImage(
                            imageName: "meetingcalander",
                            width: 20,
                            height: 20,
                          ),
                          const SizedBox(width: 8),
                          OpenSansText(
                            date,
                            fontSize: 14,
                            color: AppColors.lightBlack,
                            fontWeight: FontWeight.w400,
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Time section
                      Row(
                        children: [
                          CustomSvgImage(
                            imageName: 'meetingtime',
                            width: 20,
                            height: 20,
                          ),
                          const SizedBox(width: 8),
                          OpenSansText(
                            timeRange,
                            fontSize: 14,
                            color: AppColors.lightBlack,
                            fontWeight: FontWeight.w400,
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Meeting platform/location from the meeting object
                      Row(
                        children: [
                          CustomSvgImage(
                            imageName: 'meetinglocation',
                            width: 20,
                            height: 20,
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: OpenSansText(
                              capitalizeFirstWordOnly(
                                _getMeetingLocationText(),
                              ),
                              fontSize: 14,
                              color: AppColors.lightBlack,
                              fontWeight: FontWeight.w400,
                              overflow: TextOverflow.visible,
                              maxLines: 3,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      //   Row(
                      //     children: [
                      //       CustomSvgImage(
                      //         imageName: 'ic_description',
                      //         width: 20,
                      //         height: 20,
                      //       ),
                      //       SizedBox(width: 8),
                      //       Expanded(
                      //         child: OpenSansText(
                      //           widget.meeting.description,
                      //           fontSize: 14,
                      //           color: AppColors.darkGreyColor,
                      //           fontWeight: FontWeight.w400,
                      //           overflow: TextOverflow.visible,
                      //         ),
                      //       ),
                      //     ],
                      //   ),
                      const SizedBox(height: 12),

                      // Meeting link section - only show if meeting has a join URL
                      // In the meeting link section where the copy button is located:
                      if (widget.meeting.isOnline &&
                          widget.meeting.onlineMeeting != null &&
                          widget.meeting.onlineMeeting!.joinUrl.isNotEmpty &&
                          !widget.meeting.isCancelled)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: AppColors.lightGreyColor2,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            color: AppColors.whiteColor,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: OpenSansText(
                                  widget.meeting.onlineMeeting!.joinUrl,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.darkGreylinkColor,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(width: 8),
                              GestureDetector(
                                onTap: () {
                                  if (TimeUtils.isMeetingEnded(
                                    widget.meeting,
                                  )) {
                                    showDialog(
                                      context: context,
                                      builder:
                                          (context) => CustomConfirmationDialog(
                                            message:
                                                'This meeting has ended - the link may no longer be valid',
                                            primaryButtonText: 'OK',
                                            onPrimaryPressed:
                                                () => Navigator.pop(context),
                                          ),
                                    );
                                  } else {
                                    Clipboard.setData(
                                      ClipboardData(
                                        text:
                                            widget
                                                .meeting
                                                .onlineMeeting!
                                                .joinUrl,
                                      ),
                                    );
                                    showDialog(
                                      context: context,
                                      builder:
                                          (context) => CustomConfirmationDialog(
                                            message:
                                                'Meeting link copied to clipboard',
                                            primaryButtonText: 'OK',
                                            onPrimaryPressed:
                                                () => Navigator.pop(context),
                                          ),
                                    );
                                  }
                                },
                                child: CustomSvgImage(imageName: "copylink"),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(height: 24),

                      // Participants section
                      // Row(
                      //   children: [
                      //     DmSansText(
                      //       'Participants',
                      //       fontSize: 14,
                      //       fontWeight: FontWeight.w500,
                      //       color: AppColors.blackColor,
                      //     ),
                      //     const SizedBox(width: 3),
                      //     DmSansText(
                      //       '(${widget.meeting.attendees?.length ?? 0})',
                      //       fontSize: 14,
                      //       fontWeight: FontWeight.w500,
                      //       color: AppColors.blackColor,
                      //     ),
                      //   ],
                      // ),
                      // const SizedBox(height: 24),

                      // if (widget.meeting.attendees != null &&
                      //     widget.meeting.attendees!.isNotEmpty)
                      //   ListView.builder(
                      //     shrinkWrap: true,
                      //     physics: const NeverScrollableScrollPhysics(),
                      //     itemCount: widget.meeting.attendees!.length,
                      //     itemBuilder: (context, index) {
                      //       final attendee = widget.meeting.attendees![index];

                      //       return Padding(
                      //         padding: const EdgeInsets.only(bottom: 24.0),
                      //         child: Row(
                      //           children: [
                      //             // Participant avatar
                      //             CircleAvatar(
                      //               radius: 20,
                      //               backgroundColor:
                      //                   AppColors.calanderbordercolor2,
                      //               child: OpenSansText(
                      //                 attendee.emailAddress.name.isNotEmpty
                      //                     ? attendee.emailAddress.name[0]
                      //                         .toUpperCase()
                      //                     : 'U',
                      //                 color: AppColors.blackColor,
                      //                 fontSize: 18,
                      //                 fontWeight: FontWeight.bold,
                      //               ),
                      //             ),
                      //             const SizedBox(width: 12),

                      //             // Participant name only (no email)
                      //             Expanded(
                      //               child: OpenSansText(
                      //                 attendee.emailAddress.name,
                      //                 fontSize: 16,
                      //                 fontWeight: FontWeight.w400,
                      //                 color: AppColors.blackColor,
                      //               ),
                      //             ),

                      //             // Participant status
                      //             OpenSansText(
                      //               _getAttendeeStatus(attendee),
                      //               fontSize: 12,
                      //               color: _getStatusColor(
                      //                 _getAttendeeStatus(attendee),
                      //               ),
                      //               fontWeight: FontWeight.w400,
                      //             ),
                      //           ],
                      //         ),
                      //       );
                      //     },
                      //   )
                      // else
                      //   Padding(
                      //     padding: EdgeInsets.symmetric(vertical: 8.0),
                      //     child: OpenSansText(
                      //       'No participants',
                      //       fontSize: 14,
                      //       color: AppColors.mediumGreyColor,
                      //     ),
                      //   ),
                    ],
                  ),
                ),
              ),
            ),

            // Bottom action buttons
            Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 32.0),
              child: Column(
                children: [
                  // Only show Join button for online meetings
                  if (widget.meeting.isOnline &&
                      widget.meeting.onlineMeeting != null &&
                      !widget.meeting.isCancelled)
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed:
                            TimeUtils.isMeetingEnded(widget.meeting)
                                ? null
                                : () async {
                                  if (!TimeUtils.isMeetingStarted(
                                    widget.meeting,
                                  )) {
                                    // Show warning dialog for early join attempt
                                    await showDialog(
                                      context: context,
                                      builder:
                                          (context) => CustomConfirmationDialog(
                                            message:
                                                'This meeting will begin at ${TimeUtils.formatTimeForDisplay(widget.meeting.time, DateTime.parse(widget.meeting.date))}',
                                            primaryButtonText: 'OK',
                                            onPrimaryPressed:
                                                () => Navigator.pop(context),
                                          ),
                                    );
                                  } else {
                                    _joinMeeting(context);
                                  }
                                },
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              TimeUtils.isMeetingEnded(widget.meeting)
                                  ? AppColors.lightGreyColor2
                                  : AppColors.viewColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: DmSansText(
                          'Join',
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color:
                              TimeUtils.isMeetingEnded(widget.meeting)
                                  ? AppColors.lightGreyshade
                                  : AppColors.whiteColor,
                        ),
                      ),
                    ),
                  if (widget.meeting.isOnline) const SizedBox(height: 8),
                  // RSVP button - styled like Join button when it's the only button
                  if (!widget.meeting.isCancelled)
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: _buildRSVPButton(context),
                    ),
                  SizedBox(height: 18),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(onTap: (p0) {}),
    );
  }

  Widget _buildRSVPButton(BuildContext context) {
    final isOnline = widget.meeting.isOnline;
    final isEnded = TimeUtils.isMeetingEnded(widget.meeting);
    void handlePressed() {
      if (isEnded) {
        showDialog(
          context: context,
          builder:
              (context) => CustomConfirmationDialog(
                message: 'This event has already ended',
                primaryButtonText: 'OK',
                onPrimaryPressed: () => Navigator.pop(context),
              ),
        );
      } else {
        _showRSVPBottomSheet(context);
      }
    }

    return isOnline
        ? OutlinedButton(
          onPressed: handlePressed,
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: AppColors.meetingdetailsbuttoncolor),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: DmSansText(
            'RSVP',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.meetingdetailsbuttoncolor,
          ),
        )
        : ElevatedButton(
          onPressed: handlePressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.meetingdetailsbuttoncolor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: DmSansText(
            'RSVP',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.whiteColor,
          ),
        );
  }

  Future<void> _joinMeeting(BuildContext context) async {
    if (widget.meeting.onlineMeeting == null ||
        widget.meeting.onlineMeeting!.joinUrl.isEmpty) {
      _showSnackBar(context, 'No meeting link available', isError: true);
      return;
    }

    final joinUrl = widget.meeting.onlineMeeting!.joinUrl;

    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );
      bool launched = false;

      if (joinUrl.contains('teams.microsoft.com')) {
        final teamsAppUrl = _convertToTeamsAppUrl(joinUrl);
        if (teamsAppUrl != null) {
          try {
            launched = await launchUrl(
              Uri.parse(teamsAppUrl),
              mode: LaunchMode.externalApplication,
            );
          } catch (e) {
            print('Teams app launch failed: $e');
            launched = false;
          }
        }
      }

      if (!launched) {
        final uri = Uri.parse(joinUrl);
        launched = await launchUrl(uri, mode: LaunchMode.externalApplication);
      }

      Navigator.of(context, rootNavigator: true).pop();

      if (!launched) {
        await Clipboard.setData(ClipboardData(text: joinUrl));
        _showSnackBar(
          context,
          'Could not open automatically. Meeting link copied to clipboard',
          isError: true,
        );
      }
    } catch (e) {
      if (Navigator.of(context, rootNavigator: true).canPop()) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      _showSnackBar(
        context,
        'Error joining meeting: ${e.toString()}',
        isError: true,
      );
    }
  }

  String? _convertToTeamsAppUrl(String webUrl) {
    try {
      final uri = Uri.parse(webUrl);
      if (uri.host.contains('teams.microsoft.com')) {
        final path = uri.path;
        final queryParams = uri.queryParameters;

        if (path.contains('/l/meetup-join/')) {
          final meetingId = queryParams['meetingId'] ??
              queryParams['tid'] ??
              queryParams['thread.id'];

          if (meetingId != null) {
            return 'msteams://teams.microsoft.com/l/meetup-join/$path?${uri.query}';
          }
        }

        return 'msteams://${uri.host}${uri.path}?${uri.query}';
      }

      return null;
    } catch (e) {
      print('Error converting to Teams app URL: $e');
      return null;
    }
  }

  void _showSnackBar(
    BuildContext context,
    String message, {
    bool isError = false,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.red : AppColors.green,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'OK',
          textColor: AppColors.whiteColor,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // Show RSVP bottom sheet
  Future<void> _showRSVPBottomSheet(BuildContext context) async {
    // Check if meeting has an ID
    if (widget.meeting.id.isEmpty) {
      _showSnackBar(
        context, 
        'Cannot RSVP: Meeting ID not found', 
        isError: true
      );
      return;
    }

    final result = await showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      enableDrag: true,
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: RSVPBottomSheet(
            eventId: widget.meeting.id, 
          ),
        );
      },
    );

    if (result != null) {
      print('[MeetingDetails] RSVP result: $result');
      
      // Refresh the calendar data to show updated status
      ref.read(graphCalendarProvider.notifier).refreshEvents();
    }
  }

  // Fixed attendee status method
  String _getAttendeeStatus(Attendee attendee) {
    if (attendee.status.response == "none") {
      return 'Pending';
    }

    final response = attendee.status.response.toLowerCase().trim();

    if (response.isEmpty) {
      return 'Pending';
    } else if (response == 'accepted') {
      return 'Accepted';
    } else if (response == 'declined') {
      return 'Declined';
    } else {
      return 'Pending';
    }
  }

  // Helper methods
  Color _getStatusColor(String status) {
    switch (status) {
      case 'Accepted':
        return AppColors.green;
      case 'Declined':
        return AppColors.Orange;
      case 'Tentative':
        return Colors.orange;
      case 'Organizer':
        return Colors.blue;
      case 'Pending':
      default:
        return AppColors.goldenYellow;
    }
  }

  // Get location text based on meeting data
  String _getMeetingLocationText() {
    if (widget.meeting.location != null &&
        widget.meeting.location!.isNotEmpty) {
      return widget.meeting.location!;
    } else if (widget.meeting.isOnline) {
      // For online meetings without a location, use the provider name if available
      if (widget.meeting.onlineMeetingProvider.isNotEmpty) {
        if (widget.meeting.onlineMeetingProvider == 'teamsForBusiness') {
          return 'Microsoft Teams';
        }
        return widget.meeting.onlineMeetingProvider;
      }
      return "Microsoft Teams"; // Default for online meetings
    }
    return "Microsoft Teams"; // Default fallback
  }
}
