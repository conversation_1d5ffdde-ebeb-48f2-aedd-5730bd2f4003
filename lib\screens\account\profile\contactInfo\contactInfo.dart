
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/spacing/padding.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/screens/account/profile/components/personInfoWidget.dart';
import 'package:seawork/screens/account/profile/models/employeeInfoModel.dart';
import 'package:seawork/screens/account/profile/repository/profileRepository.dart';
import 'package:seawork/utils/style/colors.dart';

// StateNotifier to manage the selected index
class BottomNavBarNotifier extends StateNotifier<int> {
  BottomNavBarNotifier() : super(3);

  void updateIndex(int index) {
    state = index;
  }
}

// StateNotifier to manage the selected demographic field
class DemographicFieldNotifier extends StateNotifier<String?> {
  DemographicFieldNotifier() : super(null);

  void updateSelectedField(String label) {
    state = label;
  }
}

// Provider for the BottomNavBarNotifier
final bottomNavBarProvider =
    StateNotifierProvider<BottomNavBarNotifier, int>((ref) {
  return BottomNavBarNotifier();
});

// Provider for the DemographicFieldNotifier
final demographicFieldProvider =
    StateNotifierProvider<DemographicFieldNotifier, String?>((ref) {
  return DemographicFieldNotifier();
});

class ContactInfo extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(bottomNavBarProvider);
    final selectedFieldDemographic = ref.watch(demographicFieldProvider);
    final profileRepository = ref.read(profileRepositoryProvider);

    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(
        title: "Contact info",
        showActionIcon: true,
      ),
      body: SafeArea(
        child: Padding16x0(
          Column(
            children: [
              Padding0x16(
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                      BoxShadow(
                        color: AppColors.boxshadowcolor.withOpacity(0.25),
                        blurRadius: 9.6,
                        spreadRadius: 0,
                        offset: const Offset(0, 0), 
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 16),
                      Padding20x0(
                        Padding0x8(
                         DMSans700Large(16,
                          'Communication',
                          AppColors.blackColor,
                        ),
                      ),
                      ),
                      Padding0x12(
                        Padding20x0(
                          const Divider(height: 1, color: AppColors.lightBlueBackgroundColor),
                        ),
                      ),
                      FutureBuilder<EmployeeDetailModel?>(
                        future: profileRepository.getProfileDetails(),
                        builder: (context, snapshot) {
                          if (!snapshot.hasData ||
                                  snapshot.data!.items.isEmpty ??
                              true) {
                            return const Center(
                                child: CircularProgressIndicator());
                          }

                          final employeeDetail = snapshot.data!.items.first;
                          return Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              SelectableRow(
                                label: "Work email",
                                value: employeeDetail.workEmail ?? '',
                                isSelected:
                                    selectedFieldDemographic == 'Work email',
                                onTap: (label) {
                                  ref
                                      .read(demographicFieldProvider.notifier)
                                      .updateSelectedField(label);
                                },
                              ),
                              Padding0x8(
                                SelectableRow(
                                  label: "Home address",
                                  value: employeeDetail.addressLine1 ?? 'Null',
                                  isSelected: selectedFieldDemographic ==
                                      'Home address',
                                  onTap: (label) {
                                    ref
                                        .read(demographicFieldProvider.notifier)
                                        .updateSelectedField(label);
                                  },
                                  bottomPadding: 8,
                                  subText:  employeeDetail.city ?? '' ,
                                ),
                              ),
                              
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(
        onTap: (index) {
          ref.read(bottomNavBarProvider.notifier).updateIndex(index);
        },
      ),
    );
  }
}
