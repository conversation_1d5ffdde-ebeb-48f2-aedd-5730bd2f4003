import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/screens/employee/absence/providers/absenceListNotifier.dart';
import 'package:seawork/screens/employee/absence/providers/tabProvider.dart';
import 'package:seawork/screens/employee/calendar/components/customCompactCalendar.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_types_provider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/widget/customSearchBar.dart';

class CustomTabBar extends ConsumerStatefulWidget {
  final String iconClicked;
  final int? length;
  final void Function(String) onTabChanged; // Callback to notify parent
  final Widget Function(
    String status, {
    int? selectedTabIndex,
    String? iconClicked,
  })
  buildTabContent;
  final ValueChanged<String>? onSearchQueryChanged;
  final VoidCallback? onResetFilters; // Add reset callback
  final String? resetKey; // Add reset key parameter
  final List<int>? counts; // Add counts parameter

  const CustomTabBar({
    Key? key,
    required this.iconClicked,
    this.length,
    required this.buildTabContent,
    required this.onTabChanged,
    this.onSearchQueryChanged,
    this.onResetFilters, // Add this parameter
    this.resetKey, // Add this parameter
    this.counts, // Add this parameter
  }) : super(key: key);

  @override
  ConsumerState<CustomTabBar> createState() => _CustomTabBarState();
}

class _CustomTabBarState extends ConsumerState<CustomTabBar>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  bool showSearchField = false;
  final TextEditingController searchController = TextEditingController();
  late TabController _tabController;
  final List<String> tabStatuses = [
    'pending',
    'approved',
    'rejected',
    'withdrawn',
    'Draft',
  ];
  bool isLoading = false;
  String? _previousResetKey;
  bool _isPageActive = true;

  void toggleSearch() {
    setState(() {
      showSearchField = !showSearchField;
      if (!showSearchField) {
        searchController.clear();
        widget.onSearchQueryChanged?.call('');
      }
    });
  }
 void _clearFilters() {
    ref.read(dateRangeProvider.notifier).state = null;
    // ignore: unused_result
    ref.refresh(letterRequestPendingProvider);
    // ignore: unused_result
    ref.refresh(letterRequestApprovedProvider);
     // Clear search query
  }
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Always start with pending tab (index 0)
    _tabController = TabController(
      length: widget.length ?? tabStatuses.length,
      initialIndex: 0, // Always start with pending
      vsync: this,
    );
    _tabController.addListener(_handleTabIndexChanged);

    // Reset selected tab to 0 (pending)
    Future.microtask(() {
      ref.read(selectedTabProvider.notifier).state = 0;
      _tabController.animateTo(0); // Ensure TabController is also at index 0
      widget.onTabChanged('pending'); // Trigger callback with initial status
    });

    // Clear search field
    searchController.clear();
    showSearchField = false;
    _previousResetKey = widget.resetKey;
  }

  @override
  void didUpdateWidget(CustomTabBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Reset to pending when widget updates (like coming back to page)
    if (!_isPageActive) {
      _isPageActive = true;
      Future.microtask(() {
        ref.read(selectedTabProvider.notifier).state = 0;
        _tabController.animateTo(0);
        widget.onTabChanged('pending');
        setState(() {});
      });
    }

    // Check if reset key changed (indicates filter reset needed)
    if (widget.resetKey != _previousResetKey) {
      _previousResetKey = widget.resetKey;
      // Call the reset callback when key changes
      widget.onResetFilters?.call();
      print('Filter reset triggered in CustomTabBar');
    }
  }

  void _handleTabIndexChanged() {
    if (!_tabController.indexIsChanging) {
      final newIndex = _tabController.index;
      ref.read(selectedTabProvider.notifier).state = newIndex;
      widget.onTabChanged(tabStatuses[newIndex]);
      // Force rebuild to update the UI
      setState(() {});
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      _isPageActive = false;
    } else if (state == AppLifecycleState.resumed) {
      _isPageActive = true;
      // Reset to pending when app resumes
      Future.microtask(() {
        ref.read(selectedTabProvider.notifier).state = 0;
        _tabController.animateTo(0);
        widget.onTabChanged('pending');
        setState(() {});
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    // Reset to pending tab when leaving this page
    ref.read(selectedTabProvider.notifier).state = 0;
    _isPageActive = false;

    _tabController.removeListener(_handleTabIndexChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the current selected index from the provider and counts
    final selectedTabIndex = ref.watch(selectedTabProvider);
    print('Current selected tab index: $selectedTabIndex');
    final nonNullCounts = widget.counts ?? [0, 0, 0, 0, 0];
    final [pending, approved, rejected, withdrawn, draft] =
        nonNullCounts.length >= 5
            ? nonNullCounts
            : [...nonNullCounts, ...List.filled(5 - nonNullCounts.length, 0)];

    final List<String> tabStatuses = [
      'pending',
      'approved',
      'rejected',
      'withdrawn',
      'Draft',
    ];

    // Define available tabs
    final List<Map<String, String>> tabs = [
      {'label': 'Pending', 'count': pending.toString()},
      {'label': 'Approved', 'count': approved.toString()},
      {'label': 'Rejected', 'count': rejected.toString()},
      {'label': 'Withdrawn', 'count': withdrawn.toString()},
      {'label': 'Draft', 'count': draft.toString()},
    ];

    // Limit tabs based on provided length
    final visibleTabs = tabs.sublist(0, widget.length ?? tabs.length);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Removed SizedBox wrapper - No top spacing
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Flexible(
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                indicator: UnderlineTabIndicator(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    width: 2.0,
                    color: AppColors.viewColor,
                  ),
                  insets: EdgeInsets.only(top: 12, right: 4),
                ),
                labelColor: AppColors.viewColor,
                unselectedLabelColor: AppColors.blackColor,
                dividerColor: AppColors.transparentColor,
                padding: EdgeInsets.zero,
                labelPadding: EdgeInsets.only(left: 24),
                tabAlignment: widget.length! > 2? TabAlignment.start: TabAlignment.center, // Keep as start since we're using Row alignment
                overlayColor: WidgetStateProperty.all(Colors.transparent),
                onTap: (index) {
                  ref.read(selectedTabProvider.notifier).state = index;
                  widget.onTabChanged(tabStatuses[index]);
                },
                tabs:
                    visibleTabs
                        .asMap()
                        .entries
                        .map(
                          (entry) => _buildTab(
                            entry.value['label']!,
                            entry.value['count']!,
                            entry.key,
                            selectedTabIndex,
                          ),
                        )
                        .toList(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Padding(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (showSearchField)
                Expanded(
                  child: CustomSearchField(
                    showSearchField: showSearchField,
                    searchController: searchController,
                    onSearch: () {
                      // setState(() {
                      //   isLoading = true;
                      // });
                      final query = searchController.text.trim();
                      widget.onSearchQueryChanged?.call(query);
                      // Future.delayed(Duration(seconds: 2), () {
                      //   setState(() {
                      //     isLoading = false;
                      //   });
                      // });
                    },
                    toggleSearch: toggleSearch,
                  ),
                )
              else
                IconButton(
                  icon: CustomSvgImage(imageName: "search_icon"),
                  onPressed: () {
                    setState(() {
                      showSearchField = !showSearchField;
                    });
                  },
                ),
              Container(
                key: ValueKey('filter_${widget.resetKey ?? 'default'}'),
                child: FilterBasedOnDates(
                  isLetterRequest: true,
                  isApproval: false,
                  status: tabStatuses[selectedTabIndex],
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Consumer(
            builder: (context, ref, child) {
              final dateRange = ref.watch(dateRangeProvider);
              if (dateRange == null ||
                  dateRange['startDate'] == null ||
                  dateRange['endDate'] == null) {
                return const SizedBox.shrink();
              }

              final startDate = DateFormat(
                'dd MMM yyyy',
              ).format(dateRange['startDate']!);
              final endDate = DateFormat(
                'dd MMM yyyy',
              ).format(dateRange['endDate']!);

              return Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Date range on the left
                    OpenSansText(
                      '$startDate - $endDate',
                      fontSize: 14,
                      color: AppColors.viewColor,
                      fontWeight: FontWeight.w400,
                    ),

                    // Clear filters button on the right
                      TextButton(
                          onPressed: _clearFilters,
                          child: OpenSansText(
                            'clear filters',
                            fontSize: 12,
                            color: AppColors.viewColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                  ],
                ),
              );
            },
          ),
        ),

        if (isLoading)
          Expanded(
            child: Center(
              child: CustomLoadingWidget(),
              // CircularProgressIndicator(
              //   valueColor: AlwaysStoppedAnimation<Color>(AppColors.viewColor),
              // ),
            ),
          )
        else
          _buildTabView(selectedTabIndex, visibleTabs.length),
      ],
    );
  }

  Widget _buildTab(
    String label,
    String count,
    int index,
    int selectedTabIndex,
  ) {
    final bool isSelected = index == selectedTabIndex;
    // final hideCountLabels =
    //     selectedTabIndex != 1
    //         ? ['Pending', 'Withdrawn']
    //         : ['Approved', 'Rejected'];

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Tab(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            OpenSansText(
              label,
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
              color: isSelected ? AppColors.viewColor : AppColors.blackColor,
            ),
            const SizedBox(width: 8),
            if (count.isNotEmpty &&
                label != 'Pending' &&
                label != 'Withdrawn') // Ensure count is not empty
              Stack(
                alignment: Alignment.center,
                children: [
                Container(
              width: 14,
              height: 14,
              decoration: const BoxDecoration(
                color: AppColors.Orange,
                shape: BoxShape.circle,
              ),
           
                 alignment: Alignment.center,
        
                    child: RobotoText(
                      count,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  
                ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabView(int selectedTabIndex, int tabCount) {
    final List<String> tabStatuses = [
      'pending',
      'approved',
      'rejected',
      'withdrawn',
      'draft',
    ].sublist(0, tabCount);

    return Expanded(
      child: TabBarView(
        controller: _tabController,
        physics: const BouncingScrollPhysics(),
        children: List.generate(tabCount, (index) {
          return widget.buildTabContent(
            tabStatuses[index],
            iconClicked: widget.iconClicked,
            selectedTabIndex: selectedTabIndex,
          );
        }),
      ),
    );
  }
}

Tab buildCustomTab(String title, String count) {
  return Tab(
    child: Row(
      children: [
        OpenSansText(title, fontSize: 12, fontWeight: FontWeight.bold),
        const SizedBox(width: 8),
        Stack(
          alignment: Alignment.center,
          children: [
            CustomPngImage(imageName: "circleorange", height: 14, width: 14),
            Center(
              child:  RobotoText(
                count,
                 fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: AppColors.whiteColor,
              
              ),
            ),
          ],
        ),
      ],
    ),
  );
}
