import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/account/profile/components/biometricBottomSheets.dart';
import 'package:seawork/utils/style/colors.dart';

class BiometricPopup {
  static void show(BuildContext context, VoidCallback onConfirmed) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: AppColors.transparentColor,
      builder: (BuildContext context) {
        double screenWidth = MediaQuery.of(context).size.width;
        double dialogWidth = screenWidth > 392 ? 358 : screenWidth - 34;

        return Dialog(
          insetPadding: EdgeInsets.symmetric(
            horizontal: (screenWidth - dialogWidth) / 2,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: AppColors.transparentColor,
          child: Container(
            width: dialogWidth,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.statuscolour,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                OpenSansText(
                  "Biometric aren't set up",
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.blackColor,
                ),
                SizedBox(height: 8),
                OpenSansText(
                  "To enable this feature, first set up face or\nfingerprint unlock in your device settings.",
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: AppColors.blackColor,
                ),
                SizedBox(height: 20),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      BiometricBottomSheets.showFingerprintBottomSheet(
                        context,
                        onConfirmed,
                      );
                    },
                    child: OpenSansText(
                      "OK",
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.blackColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
