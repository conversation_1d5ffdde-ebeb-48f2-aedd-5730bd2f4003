import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customDropDown.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/claim/components/claimtype.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customlinkscreen.dart';
import 'package:seawork/components/widget/customDropdownWithCheckboxes.dart';
import 'package:seawork/components/widget/commentFeild.dart';
import 'package:seawork/components/widget/headingText.dart';

class EducationAllowanceForm extends StatefulWidget {
  final List<String> academicYearOptions;
  final List<String> childNameOptions;
  final bool showClaimTypeDropdown;
  final String? initialClaimType; // Added this parameter
  final Function(
    String? academicYear,
    Set<String> childNames,
    Map<String, TextEditingController> childAmountControllers,
    List<String> links,
    String comments,
  )
  onSubmit;
  final bool showAddButton;
  final bool hideAppBar;
  final String? appBarTitle;

  EducationAllowanceForm({
    Key? key,
    required this.academicYearOptions,
    this.showClaimTypeDropdown = false,
    this.initialClaimType, // Added this parameter
    required this.childNameOptions,
    required this.onSubmit,
    this.showAddButton = false,
    this.hideAppBar = false,
    this.appBarTitle,
  }) : super(key: key);

  @override
  _EducationAllowanceFormState createState() => _EducationAllowanceFormState();
}

class _EducationAllowanceFormState extends State<EducationAllowanceForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final ScrollController _scrollController = ScrollController();

  String? selectedAcademicYear = '2025-2026';
  Set<String> selectedChildNames = {};
  Map<String, TextEditingController> childAmountControllers = {};
  Map<String, String> selectedFiles = {};
  List<TextEditingController> linkControllers = [];
  late TextEditingController additionalTextController;
  late TextEditingController commentsController;
  bool isSubmitButtonActive = false;
  bool showAcademicYearError = false;
  bool showChildNameError = false;
  bool _shouldButtonScroll = false;
  List<FileModel> uploadedFiles = [];
  String? currentClaimType; // Added claim type tracking

  void updateSubmitButtonState() {
    bool allFieldsFilled =
        selectedAcademicYear != null &&
        selectedChildNames.isNotEmpty &&
        selectedChildNames.every(
          (child) => childAmountControllers[child]?.text.isNotEmpty == true,
        );

    setState(() {
      isSubmitButtonActive = allFieldsFilled;
    });
  }

  @override
  void initState() {
    super.initState();
    additionalTextController = TextEditingController();
    commentsController = TextEditingController();
    currentClaimType =
        widget.initialClaimType ??
        "Education allowance"; // Initialize claim type

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateButtonPosition();
    });
  }

  void _updateButtonPosition() {
    // If child names selected or many fields visible, button should scroll with content
    setState(() {
      _shouldButtonScroll = selectedChildNames.length > 1;
    });
  }

  @override
  void didUpdateWidget(EducationAllowanceForm oldWidget) {
    super.didUpdateWidget(oldWidget);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateButtonPosition();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    additionalTextController.dispose();
    commentsController.dispose();
    childAmountControllers.values.forEach((controller) => controller.dispose());
    linkControllers.forEach((controller) => controller.dispose());
    super.dispose();
  }

  void _submitForm() async {
    try {
      if (_formKey.currentState!.validate()) {
        // Print all form data before submitting
        print('=== EDUCATION ALLOWANCE FORM SUBMISSION ===');
        print('Academic Year: $selectedAcademicYear');
        print('Selected Children: ${selectedChildNames.toList()}');
        print('Child Amount Details:');
        selectedChildNames.forEach((child) {
          print(
            '  - $child: ${childAmountControllers[child]?.text ?? "No amount entered"}',
          );
        });
        print('Links:');
        linkControllers.asMap().forEach((index, controller) {
          print(
            '  - Link ${index + 1}: ${controller.text.isEmpty ? "Empty" : controller.text}',
          );
        });
        print(
          'Comments: ${commentsController.text.isEmpty ? "No comments" : commentsController.text}',
        );
        print('Selected Files: ${selectedFiles.keys.toList()}');
        print(
          'Additional Text: ${additionalTextController.text.isEmpty ? "No additional text" : additionalTextController.text}',
        );
        print('=============================================');

        widget.onSubmit(
          selectedAcademicYear,
          selectedChildNames,
          childAmountControllers,
          linkControllers.map((controller) => controller.text).toList(),
          commentsController.text,
        );
      } else {
        // Handle case when form validation fails
        print('Form validation failed');
      }
    } catch (e) {
      // Handle any errors that occur during submission
      print('Error during form submission: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (selectedChildNames.length > 1 && !_shouldButtonScroll) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateButtonPosition();
      });
    }

    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: AppColors.secondaryColor,
      appBar:
          widget.hideAppBar
              ? null
              : CustomAppBar(
                title:
                    widget.appBarTitle ??
                    (widget.showClaimTypeDropdown
                        ? 'Travel claims'
                        : 'Education Allowance'),
                showBackButton: true,
              ),
      extendBodyBehindAppBar: false,
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20.0),
        child: Form(
          key: _formKey,
          child: Stack(
            children: [
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.85,
                child: ListView(
                  controller: _scrollController,
                  padding: EdgeInsets.only(
                    bottom: _shouldButtonScroll ? 10 : 100,
                  ),
                  children: [
                    if (widget.showClaimTypeDropdown) ...[
                      HeadingText(text: "Claim type", hasAsterisk: true),
                      const SizedBox(height: 8),
                      ClaimTypeSelector(
                        initialClaimType: currentClaimType,
                        onClaimTypeChanged: (newClaimType) {
                          setState(() {
                            currentClaimType = newClaimType;
                          });
                        },
                      ),
                      const SizedBox(height: 24),
                    ],
                    CustomDropdown(
                      label: "Academic year",
                      hasAsterisk: true,
                      options: widget.academicYearOptions,
                      selectedValue: selectedAcademicYear,
                      onChanged: (value) {
                        setState(() {
                          selectedAcademicYear = value;
                          showAcademicYearError = false;
                        });
                        updateSubmitButtonState();
                      },
                      showError: showAcademicYearError,
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: AppColors.infoColor,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              'i',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.01,
                        ),
                        OpenSansText(
                          'Eligible to apply once a year',
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: AppColors.infoColor,
                          letterSpacing: 0,
                        ),
                      ],
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.03),
                    CustomDropdownWithCheckboxes(
                      label: "Child list",
                      hintText: "Select child",
                      hasAsterisk: true,
                      options: widget.childNameOptions,
                      selectedValues: selectedChildNames,
                      onChanged: (values) {
                        setState(() {
                          selectedChildNames = values;
                          showChildNameError = false;

                          final newControllers =
                              Map<String, TextEditingController>.from(
                                childAmountControllers,
                              );

                          for (String child in values) {
                            if (!newControllers.containsKey(child)) {
                              newControllers[child] = TextEditingController();
                            }
                          }

                          newControllers.removeWhere((child, controller) {
                            if (!values.contains(child)) {
                              controller.dispose();
                              return true;
                            }
                            return false;
                          });

                          childAmountControllers = newControllers;
                        });
                        updateSubmitButtonState();
                        _updateButtonPosition();
                      },
                      showError: showChildNameError,
                    ),
                    SizedBox(height: MediaQuery.of(context).size.height * 0.01),
                    if (selectedChildNames.isNotEmpty) ...[
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: OpenSansText(
                          selectedChildNames
                              .map(
                                (child) =>
                                    "Child ${selectedChildNames.toList().indexOf(child) + 1}: $child",
                              )
                              .join(", "),
                          fontWeight: FontWeight.w400,
                          fontSize: 14,
                          fontheight: 1.6,
                          color: const Color.fromARGB(255, 186, 160, 160),
                        ),
                      ),
                      SizedBox(
                        height: MediaQuery.of(context).size.height * 0.02,
                      ),
                      ...selectedChildNames.map((child) {
                        int childIndex =
                            selectedChildNames.toList().indexOf(child) + 1;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              text: TextSpan(
                                text: "Child $childIndex amount",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.blackColor,
                                ),
                                children: [
                                  TextSpan(
                                    text: " *",
                                    style: TextStyle(
                                      color: Colors.red,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 8),
                            Container(
                              height: 43,
                              child: TextField(
                                controller: childAmountControllers[child],
                                decoration: InputDecoration(
                                  fillColor: AppColors.whiteColor,
                                  filled: true,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 12,
                                  ),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: AppColors.lightGreyColor2,
                                      width: 1,
                                    ),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: AppColors.lightGreyColor2,
                                      width: 1,
                                    ),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    borderSide: BorderSide(
                                      color: Colors.blue,
                                      width: 2,
                                    ),
                                  ),
                                  hintText:
                                      "Enter amount for child $childIndex",
                                  hintStyle: TextStyle(
                                    color: AppColors.lightGreyshade,
                                    fontFamily: 'Open Sans',
                                    fontSize: 14,
                                  ),
                                  isDense: true,
                                ),
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(6),
                                ],
                                onChanged: (value) {
                                  updateSubmitButtonState();
                                },
                              ),
                            ),
                            SizedBox(height: 20),
                          ],
                        );
                      }).toList(),
                      DmSansText(
                        "Attachment",
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.blackColor,
                      ),
                      AttachmentField(
                        uploadedFiles: uploadedFiles,
                        onFilesChanged: (files) {
                          setState(() {
                            uploadedFiles = files;
                          });
                        },
                      ),
                      SizedBox(height: 20),
                      DmSansText(
                        "Link",
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppColors.blackColor,
                      ),
                      SizedBox(height: 8),
                      LinkInputList(),
                      SizedBox(height: 20),
                      CommentField(
                        heading: "Comments",
                        controller: commentsController,
                        hintText: "Enter comments",
                      ),
                    ],
                    if (_shouldButtonScroll) ...[
                      SizedBox(height: 32),
                      ElevatedButton(
                        onPressed: isSubmitButtonActive ? _submitForm : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.viewColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          disabledBackgroundColor: AppColors.lightGreyColor2,
                        ),
                        child: Container(
                          alignment: Alignment.center,
                          width: double.infinity,
                          height: 50,
                          child: DmSansText(
                            'Submit request',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color:
                                isSubmitButtonActive
                                    ? AppColors.whiteColor
                                    : AppColors.lightGreyshade,
                          ),
                        ),
                      ),
                      SizedBox(height: 18),
                    ],
                  ],
                ),
              ),
              if (!_shouldButtonScroll)
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Container(
                    padding: EdgeInsets.only(bottom: 20),
                    color: AppColors.secondaryColor,
                    child: ElevatedButton(
                      onPressed: isSubmitButtonActive ? _submitForm : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.viewColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        disabledBackgroundColor: AppColors.lightGreyColor2,
                      ),
                      child: Container(
                        alignment: Alignment.center,
                        width: double.infinity,
                        height: 50,
                        child: DmSansText(
                          'Submit request',
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color:
                              isSubmitButtonActive
                                  ? AppColors.whiteColor
                                  : AppColors.lightGreyshade,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
