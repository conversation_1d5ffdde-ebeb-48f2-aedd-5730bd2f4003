import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customIcon.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';

class LanguageCard extends StatefulWidget {
  @override
  _LanguageCardState createState() => _LanguageCardState();
}

class _LanguageCardState extends State<LanguageCard> {
  bool isArabic = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: Container(
        height: 70,
        decoration: BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color:AppColors.boxshadow,
              blurRadius: 8,
              spreadRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  // Globe icon - keep your original CustomSvgImage
                  CustomIcon(
                       imagePath: "assets/images/language.svg",
                   ),
                  SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      DmSansText(
                        'Language',
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: AppColors.blackColor,
                      ),
                      SizedBox(height: 2),
                      OpenSansText(
                        "Set language",
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.blackColor.withOpacity(0.6),
                        letterSpacing: 0,
                      ),
                    ],
                  ),
                ],
              ),
              // Language Toggle like in image
              Row(
                children: [
                  OpenSansText(
                    'English',
                    fontSize: 12,
                    fontWeight: !isArabic ? FontWeight.w700 : FontWeight.w400,
                    color:
                        isArabic
                            ? AppColors.lightGreyshade
                            : AppColors.viewColor,
                    letterSpacing: 0,
                  ),
                  SizedBox(width: 8),
                  LanguageToggleSwitch(
                    isArabic: isArabic,
                    onToggle: (value) {
                      setState(() {
                        isArabic = value;
                      });
                    },
                  ),
                  SizedBox(width: 8),
                  OpenSansText(
                    'عربي',
                    fontSize: 12,
                    fontWeight: isArabic ? FontWeight.w700 : FontWeight.w400,
                    color:
                        isArabic
                            ? AppColors.lightGreyshade
                            : AppColors.viewColor,
                    letterSpacing: 0,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class LanguageToggleSwitch extends StatelessWidget {
  final bool isArabic;
  final ValueChanged<bool> onToggle;

  const LanguageToggleSwitch({
    Key? key,
    required this.isArabic,
    required this.onToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onToggle(!isArabic),
      child: Container(
        width: 41,
        height: 22,
        decoration: BoxDecoration(
          color:
              isArabic ? AppColors.lightGreyColor2 : AppColors.lightGreyColor2,
          borderRadius: BorderRadius.circular(12),
        ),
        child: AnimatedAlign(
          duration: Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          alignment: isArabic ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            width: 18,
            height: 18,
            margin: EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: AppColors.viewColor,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 2,
                  offset: Offset(0, 1),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}