import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/box/container.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/screens/parent/childProfile/components/addNewChild.dart';
import 'package:seawork/screens/parent/childProfile/components/childPhotoCard.dart';
import 'package:seawork/screens/parent/childProfile/provider/childProvider.dart';
import 'package:seawork/utils/style/colors.dart';

class ChildDetailsScreen extends ConsumerWidget {
  const ChildDetailsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final children = ref.watch(childrenProvider);

    return SecondaryScaffoldWithAppBar(
      context,
      'Child details',
      SvgImage24x24('assets/images/appbackbutton.svg'),
      () => Navigator.pop(context),
      showHelpIcon: true,
      bodyItem:
          children.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomJpgImage(
                      imageName: 'nodata',
                      height: 120,
                      width: 120,
                    ),
                    const SizedBox(height: 16),
                    OpenSans400Large(
                      14,
                      'No data available',
                      AppColors.nodatarcolor,
                    ),
                  ],
                ),
              )
              : SizedBox(
                child: GridView.builder(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2, // Number of columns
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio:
                        3 / 4, // Adjust based on card's dimensions
                  ),
                  itemCount: children.length,
                  itemBuilder: (context, index) {
                    final child = children[index];
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) =>
                                    AddNewChildScreen(childId: child.id),
                          ),
                        );
                      },
                      child: ChildPhotoCard(child: child),
                    );
                  },
                ),
              ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(bottom: 52.0),
        child: FloatingActionButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const AddNewChildScreen(),
              ),
            );
          },
          backgroundColor: AppColors.viewColor,
          shape: const CircleBorder(),
          child: ClipOval(child: CustomSvgImage(imageName: "add_icon")),
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(onTap: (index) {}),
    );
  }
}
