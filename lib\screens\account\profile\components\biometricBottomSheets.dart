import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';

class BiometricBottomSheets {
  static void showFingerprintBottomSheet(
    BuildContext context,
    VoidCallback onSuccess,
  ) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      backgroundColor: AppColors.transparentColor,
      builder: (context) {
        return Container(
          height: 274,
          width: double.infinity,
          decoration: BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 33,),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    OpenSansText(
                      "Confirm biometric",
                      textAlign: TextAlign.center,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.blackColor,
                    ),
                    TextButton(
                      onPressed: () {
                        // Navigator.pop(context);
                        // showFaceIdDialog(context);
                      },
                      child: OpenSansText(
                        "Cancel",
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppColors.blackColor,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 33),
              OpenSansText(
                "Touch the fingerprint sensor",
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColors.blackColor,
              ),
              SizedBox(height: 54),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  showSuccessBottomSheet(context, onSuccess);
                },
                child: CustomSvgImage(
                  imageName: 'fingerscan',
                  height: 80,
                  width: 81,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static void showSuccessBottomSheet(
    BuildContext context,
    VoidCallback onDismiss,
  ) {
    showModalBottomSheet(
      context: context,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: AppColors.transparentColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          height: 274,
          width: double.infinity,
          padding: const EdgeInsets.only(top: 33,left: 20),
          decoration: BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: OpenSansText(
                  "Confirm biometric",
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.blackColor,
                ),
              ),
              SizedBox(height: 54),
              Container(
                child: CustomPngImage(
                  imageName: 'approvetick',
                  height: 151,
                  width: 148,
                ),
              ),
              SizedBox(height: 10),
            ],
          ),
        );
      },
    ).whenComplete(() {
      onDismiss();
      Navigator.of(context).pushNamed('/privacy-settings/build-lock-options');
    });
  }

  static void showFaceIdDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        // Start the timer when the dialog is shown
        Future.delayed(Duration(seconds: 2), () {
          Navigator.of(context).pop(); // Close the current dialog
          _showFaceIdSuccessImage(context); // Show success dialog
        });

        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          content: SizedBox(
            width: 169,
            height: 168,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomSvgImage(imageName: 'facescan', height: 60, width: 60),
                SizedBox(height: 8),
                OpenSansText(
                  "Face ID",
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  static void _showFaceIdSuccessImage(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        // Automatically close after 1 second and navigate
        Future.delayed(Duration(seconds: 1), () {
          Navigator.of(context).pop(); // Close success dialog
          Navigator.of(
            context,
          ).pushNamed('/privacy-settings/build-lock-options');
        });

        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          content: SizedBox(
            width: 169,
            height: 168,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomPngImage(
                  imageName: 'approvetick',
                  height: 151,
                  width: 148,
                ),
                SizedBox(height: 8),
                OpenSansText(
                  "Face ID",
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
