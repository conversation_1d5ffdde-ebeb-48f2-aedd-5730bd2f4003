import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/bottomNavigationBar/curvedNavigationBar.dart';
import 'package:seawork/components/bottomNavigationBar/curvedNavigationBarItem.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/dashboard/parentDashboard/profileDashboard.dart';
import 'package:seawork/screens/employee/calender/calenderScreen.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/dashboard/mainDashboard/mainDashboard.dart';
import 'package:seawork/screens/account/profile/myProfile.dart';

final bottomNavBarProvider = StateProvider<int>((ref) => 0);

class CustomBottomNavigationBar extends ConsumerWidget {
  final Function(int) onTap;

  CustomBottomNavigationBar({Key? key, required this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Future<bool> isPmsProfile() async {
      final profiles = await PreferencesUtils.getUserProfiles();
      return profiles != null &&
          profiles.isNotEmpty &&
          profiles.first['Type'] == 'PMS';
    }

    final int selectedIndex = ref.watch(bottomNavBarProvider);

    return WillPopScope(
      onWillPop: () async {
        if (selectedIndex != 0) {
          _navigateCleanly(context, 0, ref);
          return false;
        }
        return true;
      },
      child: SafeArea(
        bottom: true,
        child: CurvedNavigationBar(
          backgroundColor: AppColors.transparentColor,
          color: AppColors.secondaryColor,
          items: [
            _buildNavItem(
              iconName: 'ic_home',
              label: 'Home',
              isSelected: selectedIndex == 0,
            ),
            _buildNavItem(
              iconName: 'calen',
              label: 'Calendar',
              isSelected: selectedIndex == 1,
            ),
            _buildNavItem(
              iconName: 'ic_party-horn',
              label: 'Events',
              isSelected: selectedIndex == 2,
            ),
            _buildProfileNavItem(isSelected: selectedIndex == 3),
          ],
          index: selectedIndex,
          onTap: (index) {
            if (index == selectedIndex) return;
            ref.read(bottomNavBarProvider.notifier).state = index;

            Future.delayed(Duration(milliseconds: 100), () async {
              final isPms = await isPmsProfile();

              if (index == 0) {
                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            isPms ? ProfileDashboard() : MainDashboard(),
                  ),
                  (route) => false,
                );
              } else if (index == 1) {
                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(builder: (context) => CalendarScreen()),
                  (route) => route.settings.name == '/',
                );
              } else if (index == 3) {
                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(builder: (context) => ProfileScreen()),
                  (route) => route.settings.name == '/',
                );
              }
            });
          },
          height: 63,
          animationCurve: Curves.easeOut,
        ),
      ),
    );
  }

  CurvedNavigationBarItem _buildNavItem({
    required String iconName,
    required String label,
    required bool isSelected,
  }) {
    return CurvedNavigationBarItem(
      child: CircleAvatar(
        radius: 16,
        backgroundColor: AppColors.transparentColor,
        child: CustomSvgImage(
          imageName: iconName,
          color: isSelected ? AppColors.viewColor : AppColors.bottomNavColor,
        ),
      ),
      label: label,
      labelStyle: _labelStyle(isSelected),
    );
  }

  CurvedNavigationBarItem _buildProfileNavItem({required bool isSelected}) {
    return CurvedNavigationBarItem(
      child: CircleAvatar(
        radius: 16,
        backgroundColor: AppColors.transparentColor,
        child: CustomPngImage(
          imageName: isSelected ? "bott" : "ElipseImage",
          height: 24,
          width: 24,
        ),
      ),
      label: 'Profile',
      labelStyle: _labelStyle(isSelected),
    );
  }

  void _navigateCleanly(BuildContext context, int index, WidgetRef ref) {
    Widget screen;
    switch (index) {
      case 0:
        screen = const MainDashboard();
        break;
      case 1:
        screen = const CalendarScreen();
        break;
      case 2:
        // Added case for Events screen (assuming it exists)
        screen = const MainDashboard(); // Replace with actual Events screen
        break;
      case 3:
        screen = const ProfileScreen();
        break;
      default:
        screen = const MainDashboard();
    }

    Navigator.of(context).pushAndRemoveUntil(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => screen,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // Clean fade transition without any mirror effects
          return FadeTransition(
            opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(parent: animation, curve: Curves.easeInOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 350),
      ),
      (route) => false,
    );
  }

  TextStyle _labelStyle(bool isSelected) {
    return GoogleFonts.openSans(
      fontSize: 10,
      fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
      color: isSelected ? AppColors.viewColor : AppColors.bottomNavColor,
    );
  }
}
