
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/form/searchBar.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/spacing/padding.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/noDataFound.dart';
import 'package:seawork/screens/account/Document/components/documentDetail.dart';
import 'package:seawork/screens/account/Document/components/documentTypebottomSheet.dart';
import 'package:seawork/screens/account/Document/model/documentModel.dart';
import 'package:seawork/screens/account/document/repositorty/documentRepository.dart';
import 'package:seawork/screens/account/profile/components/detailInfo.dart';
import 'package:seawork/screens/account/profile/components/docItem.dart';
import 'package:seawork/screens/account/profile/components/filterDropDown.dart';
import 'package:seawork/utils/style/colors.dart';

// Riverpod Providers
final approvedDocumentsProvider = FutureProvider.autoDispose<MyDocumentModel>((ref) async {
  final repository = ref.read(documentRepositoryProvider);
  return await repository.getApplicationDocumentsDirectory(status: 'Approved');
});

final pendingDocumentsProvider = FutureProvider.autoDispose<MyDocumentModel>((ref) async {
  final repository = ref.read(documentRepositoryProvider);
  return await repository.getApplicationDocumentsDirectory(status: 'Pending');
});

final currentDocumentFilterProvider = StateProvider<String>((ref) => 'Approved');

class MyDocument extends ConsumerStatefulWidget {
  const MyDocument({super.key});

  @override
  ConsumerState<MyDocument> createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends ConsumerState<MyDocument>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  bool _showSearch = false;
  int? _expandedCardIndex;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _showDocumentUploadModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.5),
      isDismissible: true,
      enableDrag: true,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          minChildSize: 0.3,
          maxChildSize: 0.85,
          expand: false,
          builder: (context, scrollController) {
            return ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20.0),
                topRight: Radius.circular(20.0),
              ),
              child: PrimaryScrollController(
                controller: scrollController,
                child: const DocumentUploadModal(),
              ),
            );
          },
        );
      },
    );
  }

  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      _expandedCardIndex = null;
      if (!_showSearch) {
        _searchController.clear();
      }
    });
  }

  void _searchForDocumentType(String? type) {
    // Implement search if needed
  }

  void _updateStatusFilter(String? status) {
    if (status == null) return;
    ref.read(currentDocumentFilterProvider.notifier).state = status;
    
    // Refresh the appropriate provider based on the selected status
    if (status == 'Approved') {
      ref.invalidate(approvedDocumentsProvider);
    } else if (status == 'Pending') {
      ref.invalidate(pendingDocumentsProvider);
    }
  }

  String _formatDocumentType(String? documentType) {
    if (documentType == null) return 'Document';
    return documentType
        .replaceAll('Upload', '')
        .replaceAll('Request', '')
        .trim();
  }

  Widget _buildDocumentItem(Item item, BuildContext context, int index) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: AppColors.boxshadowcolor.withOpacity(0.1),
            blurRadius: 9.6,
            spreadRadius: 0,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: GestureDetector(
        onTap: () {
          if (_expandedCardIndex == index) {
            _showDocumentDetailsModal(context, item);
          }
        },
        child: CardSectionTwo(
          title: _formatDocumentType(item.documentType),
          subtitle: item.dateTo != null ? _formatDate(item.dateTo!) : '',
          isSubtitleRed: _isDocumentExpired(item),
          showTrailing: true,
          trailingArrowType: "right_up",
          onTap: () {
            setState(() {
              if (_expandedCardIndex == index) {
                _expandedCardIndex = null;
              } else {
                _expandedCardIndex = index;
              }
            });
          },
          children: _expandedCardIndex == index
              ? [
                  Padding0x8(const SizedBox()),
                  Divider(
                    thickness: 1,
                    indent: 9,
                    endIndent: 9,
                    color: AppColors.lightBlueBackgroundColor,
                  ),
                  Padding0x8(const SizedBox()),
                  DocumentItemWidget(
                    title: _formatDocumentType(item.documentType),
                    status: _getDocumentStatus(item),
                    screenWidth: MediaQuery.of(context).size.width,
                  ),
                ]
              : [],
        ),
      ),
    );
  }

  Widget _buildDocumentList(AsyncValue<MyDocumentModel> asyncDocs) {
    return asyncDocs.when(
      loading: () => Center(
        child: CircularProgressIndicator(color: AppColors.viewColor),
      ),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: AppColors.viewColor,
            ),
            const SizedBox(height: 16),
            OpenSansText(
              error is DioException
                  ? (error.type == DioExceptionType.connectionError ||
                          error.type == DioExceptionType.connectionTimeout
                      ? 'No internet connection'
                      : 'Failed to load documents')
                  : 'An error occurred',
              textAlign: TextAlign.center,
              fontSize: 12,
              color: AppColors.blackColor,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.viewColor,
              ),
              onPressed: () => ref.refresh(asyncDocs is AsyncValue<MyDocumentModel> && 
                  ref.read(currentDocumentFilterProvider) == 'Approved'
                      ? approvedDocumentsProvider
                      : pendingDocumentsProvider),
              child: OpenSansText(
                "Retry",
                color: AppColors.whiteColor,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
      data: (documentModel) {
        final items = documentModel.items ?? [];
        if (items.isEmpty) {
          final filter = ref.read(currentDocumentFilterProvider);
          return Center(
            child: OpenSansText(
              'No ${filter.toLowerCase()} documents found',
              fontSize: 14,
              color: AppColors.blackColor,
            ),
          );
        }

        return ListView.builder(
          shrinkWrap: true,
          itemCount: items.length,
          itemBuilder: (context, index) => 
              _buildDocumentItem(items[index], context, index),
        );
      },
    );
  }

  void _refreshCurrentTabData() {
    final currentFilter = ref.read(currentDocumentFilterProvider);
    if (currentFilter == 'Approved') {
      ref.refresh(approvedDocumentsProvider);
    } else {
      ref.refresh(pendingDocumentsProvider);
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentFilter = ref.watch(currentDocumentFilterProvider);
    final documentsAsync = currentFilter == 'Approved'
        ? ref.watch(approvedDocumentsProvider)
        : ref.watch(pendingDocumentsProvider);

    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(
        title: "My documents",
        showActionIcon: true,
        onBackPressed: () => Navigator.pop(context),
      ),
      body: SafeArea(
        child: Padding20x0(
          Column(
            children: [
              Container(
                height: 40,
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.whiteColor,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowBoxColor,
                      blurRadius: 9.6,
                      spreadRadius: 0,
                      offset: Offset.zero,
                    ),
                  ],
                ),
                child: TabBar(
                  controller: _tabController,
                  onTap: (index) {
                    setState(() {
                      _expandedCardIndex = null; // Close card when changing tabs
                    });
                  },
                  indicator: BoxDecoration(
                    color: AppColors.viewColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  indicatorPadding: EdgeInsets.zero,
                  labelColor: AppColors.whiteColor,
                  unselectedLabelColor: AppColors.blackColor,
                  labelStyle: GoogleFonts.openSans(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: GoogleFonts.openSans(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelPadding: const EdgeInsets.symmetric(horizontal: 8),
                  tabs: const [
                    Tab(text: "Employee"),
                    Tab(text: "Parent"),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              Row(
                children: [
                  const SizedBox(height: 45),
                  Expanded(
                    child: SearchBarWidget(
                      showSearchField: _showSearch,
                      searchController: _searchController,
                      hintText: "Search by type",
                      onSearch: _searchForDocumentType,
                      onClear: () => setState(() => _showSearch = false),
                      onToggleSearch: _toggleSearch,
                      filterWidget: FilterDropdownButton(
                        onFilterChanged: _updateStatusFilter,
                      ),
                      leadingIcon: CustomSvgImage(imageName: "search_icon"),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Employee Tab
                    RefreshIndicator(
                      onRefresh: () async {
                        final filter = ref.read(currentDocumentFilterProvider);
                        if (filter == 'Approved') {
                          await ref.refresh(approvedDocumentsProvider.future);
                        } else {
                          await ref.refresh(pendingDocumentsProvider.future);
                        }
                      },
                      child: _buildDocumentList(documentsAsync),
                    ),

                    // Parent Tab
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomSvgImage(imageName: 'nodata'),
                          const SizedBox(height: 12),
                          DmSansText(
                            'No data available',
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: AppColors.blackColor,
                          ),
                          const SizedBox(height: 8),
                          DmSansText(
                            'Entries will appear here once added',
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: AppColors.blackColor,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(bottom: 70.0),
        child: FloatingActionButton(
          backgroundColor: AppColors.viewColor,
          onPressed: _showDocumentUploadModal,
          shape: const CircleBorder(),
          child: ClipOval(child: CustomSvgImage(imageName: "add_icon")),
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(onTap: (int) {}),
    );
  }

  String _formatDate(dynamic date) {
    if (date is DateTime) {
      return DateFormat('yyyy-MM-dd').format(date);
    } else if (date is String) {
      try {
        final parsedDate = DateTime.parse(date);
        return DateFormat('yyyy-MM-dd').format(parsedDate);
      } catch (e) {
        return date;
      }
    }
    return 'Invalid date';
  }

  bool _isDocumentExpired(Item item) {
    if (item.dateTo == null) return false;

    DateTime? expiryDate;
    if (item.dateTo is DateTime) {
      expiryDate = item.dateTo as DateTime;
    } else if (item.dateTo is String) {
      try {
        expiryDate = DateTime.parse(item.dateTo as String);
      } catch (e) {
        return false;
      }
    }

    return expiryDate?.isBefore(DateTime.now()) ?? false;
  }

  String _getDocumentStatus(Item item) {
    if (item.dateTo == null) return '';

    DateTime? expiryDate;
    if (item.dateTo is DateTime) {
      expiryDate = item.dateTo as DateTime;
    } else if (item.dateTo is String) {
      try {
        expiryDate = DateTime.parse(item.dateTo as String);
      } catch (e) {
        return '';
      }
    }

    return expiryDate?.isBefore(DateTime.now()) ?? false ? 'Expired' : '';
  }

  void _showDocumentDetailsModal(BuildContext context, Item item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DocumentDetailsModal(
        item: item,
      ),
    );
  }
}

class FilterDropdownButton extends ConsumerWidget {
  final Function(String?) onFilterChanged;

  const FilterDropdownButton({super.key, required this.onFilterChanged});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentFilter = ref.watch(currentDocumentFilterProvider);

    return PopupMenuButton<String>(
      onSelected: (value) {
        onFilterChanged(value);
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'Approved',
          child: Text('Approved',
            style: TextStyle(
              fontWeight: currentFilter == 'Approved' 
                  ? FontWeight.bold 
                  : FontWeight.normal,
            ),
          ),
        ),
        PopupMenuItem(
          value: 'Pending',
          child: Text('Pending',
            style: TextStyle(
              fontWeight: currentFilter == 'Pending' 
                  ? FontWeight.bold 
                  : FontWeight.normal,
            ),
          ),
        ),
      ],
      icon: CustomSvgImage(imageName: "settings_icon"),
    );
  }
}