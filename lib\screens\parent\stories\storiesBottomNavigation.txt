import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/bottomNavigationBar/curvedNavigationBar.dart';
import 'package:seawork/components/bottomNavigationBar/curvedNavigationBarItem.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';

// import 'package:seawork/screens/parent/stories/addPostBottmSheet.dart';

import 'package:seawork/screens/parent/stories/components/addPostBottomSheet.dart';
import 'package:seawork/screens/parent/stories/storiesList.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/dashboard/mainDashboard/mainDashboard.dart';


final bottomNavBarProvider = StateProvider<int>((ref) => 0);

class storiesBottomNavigationBar extends ConsumerWidget {
  final Function(int) onTap;

  storiesBottomNavigationBar({
    Key? key,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final int selectedIndex = ref.watch(bottomNavBarProvider);
    final screenWidth = MediaQuery.of(context).size.width;
   final int totalItems = 5;
   final double itemWidth = screenWidth / totalItems;

    return WillPopScope(
      onWillPop: () async {
        if (selectedIndex != 0) {
          ref.read(bottomNavBarProvider.notifier).state = 0;
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => MainDashboard()),
            (route) => false,
          );
          return false;
        }
        return true;
      },
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
       CurvedNavigationBar(
        backgroundColor: Colors.transparent, 
        color: AppColors.secondaryColor,
        items: [
          CurvedNavigationBarItem(
            child: CircleAvatar(
              radius: 16,
              backgroundColor: Colors.transparent,
              child: CustomSvgImage(
                imageName: 'ic_home',
                color: selectedIndex == 0
                    ? AppColors.viewColor
                    : AppColors.lightGreyColor,
              ),
            ),
            label: 'Home',
            labelStyle: _labelStyle(selectedIndex == 0),
          ),
   
          CurvedNavigationBarItem(
            child: CircleAvatar(
              radius: 16,
              backgroundColor: Colors.transparent,
              child: CustomSvgImage(
                imageName: selectedIndex == 1 ? 'activeAllPost' : 'allPost',
            
              ),
            ),
            label: 'All posts',
            labelStyle: _labelStyle(selectedIndex == 1),
          ),
          CurvedNavigationBarItem(
            child: CircleAvatar(
              radius: 16,
              backgroundColor: Colors.transparent,
              child: CustomSvgImage(
                imageName: selectedIndex == 2 ? 'activeAddIcon' :'addIcon',
              
              ),
            ),
            label: 'Add post',
            labelStyle: _labelStyle(selectedIndex == 2),
          ),
          CurvedNavigationBarItem(
            child: CircleAvatar(
              radius: 16,
              backgroundColor: Colors.transparent,
           child: CustomSvgImage(
                imageName: selectedIndex == 3 ?'activeForYou': 'forYou',
              
              ),
            ),
            label: 'For you',
            labelStyle: _labelStyle(selectedIndex == 3),
          ),
          CurvedNavigationBarItem(
            child: CircleAvatar(
              radius: 16,
              backgroundColor: Colors.transparent,
                child: CustomSvgImage(
                imageName: selectedIndex == 4 ? 'activeMyStories' : 'myStories',
             
              ),
            ),
            label: 'My stories',
            labelStyle: _labelStyle(selectedIndex == 4),
          ),
        ],
        index: selectedIndex,
        onTap: (index) {
          if (index == selectedIndex) return;

          ref.read(bottomNavBarProvider.notifier).state = index;

          // Future.delayed(Duration(milliseconds: 100), () {
          //   if (index == 3) {
          //     Navigator.push(
          //       context,
          //       MaterialPageRoute(builder: (context) => ProfileScreen()),
          //     ).then((_) {
          //       ref.read(bottomNavBarProvider.notifier).state = 3;
          //     });
          //   }

          //    else if (index == 0) {
          //     Navigator.pushAndRemoveUntil(
          //       context,
          //       MaterialPageRoute(builder: (context) => MainDashboard()),
          //       (route) => false,
          //     );
          //     ref.read(bottomNavBarProvider.notifier).state = 0;
          //   }
          // });
          Future.delayed(Duration(milliseconds: 100), () {
            if (index == 2) {
                showAddPostBottomSheet(context);
  ref.read(bottomNavBarProvider.notifier).state = 2;
             
            } else if (index == 1) {
              // Navigator.push(
              //   context,
              //   MaterialPageRoute(builder: (context) =>  StoriesList()),
              // ).then((_) {
              //   ref.read(bottomNavBarProvider.notifier).state = 1;
              // });
            } else if (index == 0) {
            //   Navigator.pushAndRemoveUntil(
            //     context,
            //     MaterialPageRoute(builder: (context) => MainDashboard()),
            //     (route) => false,
            //   );
            //   ref.read(bottomNavBarProvider.notifier).state = 0;
             }
          });
        },
        height: 63,
        animationCurve: Curves.easeOut,
      ),
                 Positioned(
            bottom: 15, 
            left: itemWidth - 0.5, 
            child: Container(
              width: 1,
              height: 35,
              color: AppColors.dividerColor1,
            ),
          ),
        ],
      ),
    );
  }


  TextStyle _labelStyle(bool isSelected) {
    return GoogleFonts.openSans(
      fontSize: 10,
      fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
      color: isSelected ? AppColors.viewColor : AppColors.bottomNavColor,
    );
  }
}

