import 'package:flutter/material.dart';
import 'dart:async';
import '../model/storiesListModel.dart';
import '../repository/storiesRepository.dart';
import 'package:seawork/data/module/networkModule.dart';

class StoriesProvider extends ChangeNotifier {
  final StoriesRepository _repository;

  // Store posts in a Map by ID for more robust updates
  final Map<int, StoriesListModel> _byId = {};
  // Keep track of post order
  List<int> _postIds = [];
  
  int _currentPage = 1;
  final int _pageSize = 10;
  bool _isLoading = false;
  bool _hasMore = true;
  String _orderBy = 'desc';
  final String _userId;
  int _userType = 1;
  bool _isForYou = false;                 // <─ NEW

  // Caches for presigned URLs
  final Map<String, String> _profilePicUrlCache = {};
  final Map<String, String> _postImageUrlCache = {};
  
  // Shield for optimistic updates
  final Map<int, StoriesListModel> _localOverrides = {};

  // ─────────────────────────────────────────────
  //  ➜ 1.  Add fields for post view tracking
  // ─────────────────────────────────────────────
  final Set<int> _viewedPostIds = {};              // one-per-session rule
  final List<PostViewPayload> _pendingViews = []; // waiting to flush
  Timer? _flushTimer;
  
  // Getter that derives a list from the map for UI consumption
  List<StoriesListModel> get posts {
    // ① start with the order we already keep
    final list = _postIds.map((id) => _byId[id]!).toList();
    // ② patch each element if we have an override
    return list.map((p) => _localOverrides[p.id] ?? p).toList();
  }
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  int get userType => _userType;
  String get userId => _userId;
  int get userIdInt => int.tryParse(_userId) ?? 0;
  bool get isForYou => _isForYou;

  StoriesProvider({
    StoriesRepository? repository,
    required String userId,
  })  : _repository = repository ?? StoriesRepository(baseUrl: NetworkModule.baseUrlNMS),
        _userId = userId;

  // ---------------------------------
  // TOP: new helper to clear caches
  // ---------------------------------
  void reset() {
    _byId.clear();          // global array == empty
    _postIds.clear();
    _localOverrides.clear();
    _currentPage = 1;
    _hasMore = true;
    notifyListeners();
  }

  // ---------------------------------
  // fetchPosts()  – pagination append
  // ---------------------------------
  Future<void> fetchPosts({
    bool refresh = false,
    bool forYou = false,                 // <─ NEW
  }) async {
    if (_isLoading) return;
    _isLoading = true;
    _isForYou = forYou;                  // remember which feed we are on
    
    if (refresh) {
      reset();                      // ① wipe local array on full refresh
    }

    notifyListeners();
    try {
      final newPosts = forYou
          ? await _repository.getAllPostsForYou(
              pageNumber: _currentPage,
              pageSize  : _pageSize,
              orderBy   : _orderBy,
              userId    : _userId,
              userType  : _userType,
            )
          : await _repository.getAllPosts(page: _currentPage);
          
      for (final p in newPosts) {
        if (p.id == null) continue;
        _byId[p.id!] = p;           // ② ADD / UPDATE single post
        if (!_postIds.contains(p.id)) _postIds.add(p.id!);
      }
      _hasMore = newPosts.length == _pageSize;
      if (_hasMore) _currentPage++;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get profile picture URL for a post's CreatedBy.ProfileDocumentUpload
  Future<String?> getProfilePicUrl(String? key) async {
    debugPrint('🚨 OLD getProfilePicUrl called with key: $key');
    if (key == null || key.isEmpty) return null;
    if (_profilePicUrlCache.containsKey(key)) {
      return _profilePicUrlCache[key];
    }
    final url = await _repository.getPresignedUrl(key);
    if (url != null) {
      _profilePicUrlCache[key] = url;
    }
    return url;
  }

  // Get post image URL for a post's first PostAttachments.attachmentKey
  Future<String?> getPostImageUrl(String? key) async {
    if (key == null || key.isEmpty) return null;
    if (_postImageUrlCache.containsKey(key)) {
      return _postImageUrlCache[key];
    }
    final url = await _repository.getPresignedUrl(key);
    if (url != null) {
      _postImageUrlCache[key] = url;
    }
    return url;
  }

  // NEW: Get presigned URL using the new NestJS endpoint
  Future<String?> getPresignedUrlNew(String? key) async {
    debugPrint('🔍 getPresignedUrlNew called with key: $key');
    if (key == null || key.isEmpty) return null;
    if (_postImageUrlCache.containsKey(key)) {
      debugPrint('🔍 Found in cache: ${_postImageUrlCache[key]}');
      return _postImageUrlCache[key];
    }
    debugPrint('🔍 Calling _repository.fetchPresignedUrl...');
    final url = await _repository.fetchPresignedUrl(key);
    debugPrint('🔍 fetchPresignedUrl returned: $url');
    
    // Additional validation and debugging for the URL
    if (url != null) {
      if (url.contains('[object%20Object]')) {
        debugPrint('⚠️ URL contains [object%20Object], which may cause issues');
      }
      
      // Validate URL format
      try {
        final uri = Uri.parse(url);
        if (!uri.isAbsolute) {
          debugPrint('⚠️ URL is not absolute: $url');
          return null;
        }
        debugPrint('✅ URL is valid: $url');
      } catch (e) {
        debugPrint('❌ Invalid URL format: $url');
        debugPrint('❌ Error: $e');
        return null;
      }
      
      _postImageUrlCache[key] = url;
      return url;
    } else {
      debugPrint('❌ No URL returned from fetchPresignedUrl');
      return null;
    }
    return url;
  }

  // ---------------------------------
  // reactToPost()  – fire-and-forget
  // ---------------------------------
  Future<void> reactToPost({
    required int postId,
    required int reactionType,
    String? imageKey, // NEW: optional image key for presigned URL
  }) async {
    if (!_byId.containsKey(postId)) return;

    final original = _byId[postId]!;
    final oldType  = original.userReaction?.reactionType ?? ReactionType.none;
    final bool isOff = oldType == reactionType;

    // 1⃣ optimistic patch (unchanged)
    final int newType = isOff ? ReactionType.none : reactionType;
    final updated     = _applyLocalReaction(original, newType);
    _byId[postId]         = updated;
    _localOverrides[postId] = updated;          // ⬅️ keep it until refresh
    notifyListeners();

    // 2⃣ background call – NO await, NO rollback, just log
    unawaited(
      _repository.addOrUpdatePostReaction(
        postId      : postId,
        userType    : _userType,
        reactionType: reactionType,
        imageKey    : imageKey, // NEW: pass image key
      ).then((ok) {
        if (!ok) debugPrint('❌ react API failed (ignored by UI)');
      }),
    );
  }

  // ---------------------------------------------
  // REPORT / FLAG a post
  // ---------------------------------------------
  Future<void> reportPost({
    required int postId,
    required int reasonType,   // 1-based index that matches the API
    required String remarks,
    String? imageKey, // NEW: optional image key for presigned URL
  }) async {
    // optimistic-UI: immediately mark the post locally (optional)
    if (_byId[postId] != null) {
      _localOverrides[postId] = _byId[postId]!
          .copyWith(postStatus: 999); // 999 = "flagged locally"
      notifyListeners();
    }

    final ok = await _repository.addOrRemovePostFlags(
      postId     : postId,
      userId     : int.parse(userId),   // you already keep these two
      userType   : _userType,
      reasonType : reasonType,
      description: remarks,
      imageKey   : imageKey, // NEW: pass image key
    );

    if (ok) {
      // you might fetch the post again or show a SnackBar
    } else {
      // roll back
      _localOverrides.remove(postId);
      notifyListeners();
    }
  }

  // -------------------------------------------------------------------------
  // internal helper – returns a NEW model with counters adjusted
  // -------------------------------------------------------------------------
  StoriesListModel _applyLocalReaction(StoriesListModel story, int newType) {
    final oldType = story.userReaction?.reactionType ?? ReactionType.none;
    if (oldType == newType) return story;   // nothing to change

    ReactionCountsByGroup counts = (story.reactionCountsByGroup ??
            ReactionCountsByGroup(
              heartCount: 0,
              likeCount: 0,
              clapsCount: 0,
              sadCount:  0,
            ))
        .copyWith(); // clone

    void dec(int t) {
      switch (t) {
        case ReactionType.thumbsup: counts = counts.copyWith(likeCount: (counts.likeCount ?? 1) - 1); break;
        case ReactionType.heart:    counts = counts.copyWith(heartCount: (counts.heartCount ?? 1) - 1); break;
        case ReactionType.clap:     counts = counts.copyWith(clapsCount: (counts.clapsCount ?? 1) - 1); break;
        case ReactionType.smile:    counts = counts.copyWith(sadCount: (counts.sadCount ?? 1) - 1); break;
      }
    }
    void inc(int t) {
      switch (t) {
        case ReactionType.thumbsup: counts = counts.copyWith(likeCount: (counts.likeCount ?? 0) + 1); break;
        case ReactionType.heart:    counts = counts.copyWith(heartCount: (counts.heartCount ?? 0) + 1); break;
        case ReactionType.clap:     counts = counts.copyWith(clapsCount: (counts.clapsCount ?? 0) + 1); break;
        case ReactionType.smile:    counts = counts.copyWith(sadCount: (counts.sadCount ?? 0) + 1); break;
      }
    }
    if (oldType != ReactionType.none) dec(oldType);
    if (newType != ReactionType.none) inc(newType);

    return story.copyWith(
      reactionCountsByGroup: counts,
      userReaction: newType == ReactionType.none
          ? null
          : (story.userReaction?.copyWith(reactionType: newType) ??
              UserReaction(
                postId: story.id,
                userId: int.tryParse(_userId),
                userType: _userType,
                reactionType: newType,
              )),
    );
  }

  // ─────────────────────────────────────────────
  //  ➜ 2.  Public helper – call from UI
  // ─────────────────────────────────────────────
  void markPostViewed(int postId) {
    // Rule: same user can view a post only once in this session
    if (_viewedPostIds.contains(postId)) return;

    _viewedPostIds.add(postId);
    _pendingViews.add(
      PostViewPayload(
        postId: postId,
        userId: int.parse(userId), // existing provider field
        userType: userType,        // existing provider field
      ),
    );
    _scheduleFlush();
  }

  // ─────────────────────────────────────────────
  //  ➜ 3.  Internal: debounce + flush
  // ─────────────────────────────────────────────
  void _scheduleFlush() {
    _flushTimer?.cancel();
    _flushTimer = Timer(const Duration(seconds: 5), _flushPendingViews);
  }

  Future<void> _flushPendingViews() async {
    if (_pendingViews.isEmpty) return;

    // copy ‑ then clear queue immediately (optimistic)
    final batch = List<PostViewPayload>.from(_pendingViews);
    _pendingViews.clear();

    final ok = await _repository.addMultiplePostViews(batch);
    if (!ok) {
      // put them back so we can retry on the next flush
      _pendingViews.insertAll(0, batch);
      // optionally: exponential-back-off, but simple retry next 10 s
      _flushTimer ??= Timer(const Duration(seconds: 10), _flushPendingViews);
    }
  }

  // ─────────────────────────────────────────────
  //  ➜ 4.  Clean-up
  // ─────────────────────────────────────────────
  @override
  void dispose() {
    _flushTimer?.cancel();
    super.dispose();
  }
}
