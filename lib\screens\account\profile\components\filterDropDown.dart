
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_types_provider.dart';
import 'package:seawork/utils/style/colors.dart';

class FilterDropdownButton extends ConsumerStatefulWidget {
  const FilterDropdownButton({Key? key, required Null Function(dynamic String) onFilterChanged}) : super(key: key);

  @override
  _FilterDropdownButtonState createState() => _FilterDropdownButtonState();
}

class _FilterDropdownButtonState extends ConsumerState<FilterDropdownButton> {
  final GlobalKey _dropdownButtonKey = GlobalKey();
  String _selectedOption = ''; // Track selected option

  void _showFilterMenu() async {
    final RenderBox renderBox =
        _dropdownButtonKey.currentContext!.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    double menuWidth = size.width + 60; // Match button width

    final selected = await showMenu<String>(
      context: context,
      position: RelativeRect.fromLTRB(
        offset.dx,
        offset.dy + size.height,
        offset.dx + menuWidth,
        offset.dy + size.height + 100,
      ),
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: AppColors.lightGreyColor2, width: 1),
      ),
      items: [
        _buildPopupMenuItem('Approved'),
        _buildPopupMenuItem('Pending'),
      ],
    );

    if (selected != null) {
      setState(() {
        _selectedOption = selected;
      });
      
      // Refresh the appropriate provider based on selection
      if (selected == 'Approved') {
        ref.read(letterRequestApprovedProvider.notifier).refresh();
      } else if (selected == 'Pending') {
        ref.read(letterRequestPendingProvider.notifier).refresh();
      }
    }
  }

  PopupMenuItem<String> _buildPopupMenuItem(String value) {
    bool isSelected = _selectedOption == value;
    return PopupMenuItem<String>(
      height: 20,
      value: value,
      padding: EdgeInsets.symmetric(horizontal: 4),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.lightGreyColor2 : AppColors.whiteColor,
          borderRadius: BorderRadius.circular(4),
        ),
        alignment: Alignment.center, // Proper alignment
        child: OpenSansText(
          value,
          fontSize: 12,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.w400,
          color: AppColors.blackColor,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      key: _dropdownButtonKey,
      onPressed: _showFilterMenu,
      icon: CustomSvgImage(imageName: "settings_icon"),
    );
  }
}