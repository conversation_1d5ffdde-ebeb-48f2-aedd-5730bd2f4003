

class Attendee {
  final String type;
  final AttendeeStatus status;
  final EmailAddress emailAddress;

  Attendee({
    required this.type,
    required this.status,
    required this.emailAddress,
  });

  factory Attendee.fromJson(Map<String, dynamic> json) {
    return Attendee(
      type: json['type'] ?? '',
      status: AttendeeStatus.fromJson(json['status'] ?? {}),
      emailAddress: EmailAddress.fromJson(json['emailAddress'] ?? {}),
    );
  }
}

class AttendeeStatus {
  final String response;
  final String time;

  AttendeeStatus({
    required this.response,
    required this.time,
  });

  factory AttendeeStatus.fromJson(Map<String, dynamic> json) {
    // Make sure we're extracting the response field correctly
    String responseValue = '';
    if (json.containsKey('response')) {
      responseValue = json['response'] as String? ?? '';
    }

    return AttendeeStatus(
      response: responseValue,
      time: json['time'] as String? ?? '',
    );
  }
}

class EmailAddress {
  final String name;
  final String address;

  EmailAddress({
    required this.name,
    required this.address,
  });

  factory EmailAddress.fromJson(Map<String, dynamic> json) {
    return EmailAddress(
      name: json['name'] ?? '',
      address: json['address'] ?? '',
    );
  }
}

class OnlineMeeting {
  final String joinUrl;

  OnlineMeeting({
    required this.joinUrl,
  });

  factory OnlineMeeting.fromJson(Map<String, dynamic> json) {
    return OnlineMeeting(
      joinUrl: json['joinUrl'] ?? '',
    );
  }
}

class Meeting {
  final String id;
  final String title;
  final String date;
  final String time;
  final int durationMinutes;
  final String? location;
  final bool isOnline;
  final String onlineMeetingProvider;
  final List<Attendee>? attendees;
  final OnlineMeeting? onlineMeeting;
  final bool isCancelled;

  // Adding these fields for backward compatibility
  final String? description;
  final String? organizer;
  final DateTime? startTime;
  final DateTime? endTime;

  Meeting({
    required this.id,
    required this.title,
    required this.date,
    required this.time,
    required this.durationMinutes,
    this.location,
    required this.isOnline,
    required this.onlineMeetingProvider,
    this.attendees,
    this.onlineMeeting,
    // Backward compatibility fields
    this.description,
    this.organizer,
    this.startTime,
    this.endTime,
    this.isCancelled = false,
  });

  factory Meeting.fromJson(Map<String, dynamic> json) {
    final isCancelled = json['isCancelled'] as bool? ?? false;
    // Parse DateTime objects from start and end
    final startDateTime =
        json['start'] != null && json['start']['dateTime'] != null
            ? DateTime.parse(json['start']['dateTime'])
            : DateTime.now();

    final endDateTime =
        json['end'] != null && json['end']['dateTime'] != null
            ? DateTime.parse(json['end']['dateTime'])
            : startDateTime.add(const Duration(minutes: 30));

    // Calculate duration in minutes
    final durationMinutes = endDateTime.difference(startDateTime).inMinutes;

    // Format date as yyyy-MM-dd
    final formattedDate = "${startDateTime.year}-"
        "${startDateTime.month.toString().padLeft(2, '0')}-"
        "${startDateTime.day.toString().padLeft(2, '0')}";

    // Format time as HH:mm
    final formattedTime = "${startDateTime.hour.toString().padLeft(2, '0')}:"
        "${startDateTime.minute.toString().padLeft(2, '0')}";

    // Get location info
    String? location;
    if (json.containsKey('location') &&
        json['location'] is Map<String, dynamic> &&
        json['location']['displayName'] != null) {
      location = json['location']['displayName'] as String;
    }

    // Parse online meeting info
    final isOnline = json['isOnlineMeeting'] as bool? ?? false;

    // Parse attendees
    List<Attendee>? attendees;
    if (json.containsKey('attendees') && json['attendees'] is List) {
      attendees = (json['attendees'] as List)
          .map((item) => Attendee.fromJson(item as Map<String, dynamic>))
          .toList();
    }

    // Parse online meeting
    OnlineMeeting? onlineMeeting;
    if (json.containsKey('onlineMeeting') &&
        json['onlineMeeting'] is Map<String, dynamic>) {
      onlineMeeting =
          OnlineMeeting.fromJson(json['onlineMeeting'] as Map<String, dynamic>);
    }

    return Meeting(
      id: json['id'] ?? '',
      title: json['subject'] ?? 'Untitled Meeting',
      date: formattedDate,
      time: formattedTime,
      durationMinutes: durationMinutes,
      location: location,
      isOnline: isOnline,
      onlineMeetingProvider: json['onlineMeetingProvider'] ?? '',
      attendees: attendees,
      onlineMeeting: onlineMeeting,
      // Backward compatibility fields
      description: json['bodyPreview'],
      organizer: json['organizer']?['emailAddress']?['name'],
      startTime: startDateTime,
      endTime: endDateTime,
      isCancelled: isCancelled,
    );
  }
}
