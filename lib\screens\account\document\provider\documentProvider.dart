// // document_provider.dart
// import 'package:dio/dio.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:seawork/screens/account/Document/model/documentModel.dart';
// import 'package:seawork/screens/account/Document/repositorty/documentRepository.dart';

// // Dio instance provider
// final dioProvider = Provider<Dio>((ref) => Dio());

// // Document provider using FutureProvider
// final documentProvider = FutureProvider<MyDocumentModel?>((ref) async {
//   final dio = ref.watch(dioProvider);
//   final repository = DocumentRepository(dio);
//   return await repository.getApplicationDocumentsDirectory();
// });
