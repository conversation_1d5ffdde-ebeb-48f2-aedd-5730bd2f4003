
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';

class SelectableRow extends StatelessWidget {
  final String label;
  final String value;
  final DateTime? dateValue;
  final bool highlight;
  final bool isSelected;
  final Function(String) onTap;
  final String? subText;
  final double? bottomPadding;

  const SelectableRow({
    Key? key,
    required this.label,
    required this.value,
    this.dateValue,
    this.highlight = false,
    required this.isSelected,
    required this.onTap,
    this.subText,
    this.bottomPadding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final displayValue = dateValue != null
        ? DateFormat('dd MMM yyyy').format(dateValue!).toUpperCase()
        : value;

    return GestureDetector(
      onTap: () => onTap(label),
      child: Container(
        color: isSelected ? AppColors.lightGreyColor3 : AppColors.transparentColor,
        padding: EdgeInsets.only(
          left: 20,
          right: 20,
          bottom: bottomPadding ?? 16,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            OpenSansText(
              label,
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppColors.blackColor,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                OpenSansText(
                  displayValue,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.blackColor,
                  letterSpacing: 0,
                ),
                if (subText != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 2.0),
                    child: OpenSansText(
                      subText!,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                                       color: AppColors.blackColor,

                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}