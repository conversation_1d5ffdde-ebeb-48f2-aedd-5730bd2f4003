import { Injectable } from '@nestjs/common';
import * as FormData from 'form-data';
import { HttpService } from 'packages/http';
import { FamilyModel } from '../models/family/family.model';
import { MaritalStatusModel } from '../models/family/maritalStatus.model';

@Injectable()
export class FamilyService {
  constructor(private readonly http: HttpService) {}

  public async addUpdateFamily(model: FamilyModel): Promise<FamilyModel> {
    // -- not used
    const response = await this.http.post<FamilyModel>(
      `Family/AddUpdateFamily`,
      model,
    );
    return response;
  }

  public async addUpdateSpouse(
    model: MaritalStatusModel,
  ): Promise<MaritalStatusModel> {
    // -- not used
    const response = await this.http.post<MaritalStatusModel>(
      `Family/AddUpdateSpouse`,
      model,
    );
    return response;
  }

  public async uploadFileToS3(file: Express.Multer.File): Promise<any> {
    const form = new FormData();
    form.append('file', file.buffer, {
      filename: file.originalname,
      contentType: file.mimetype,
    });
    const response = await this.http.post<any>('upload/UploadFileToS3', form, {
      baseURL: process.env.NO_PARENT_PMS_API_URL,
      postHeaders: { 'Content-Type': 'multipart/form-data' },
    });
    return response;
  }
  public async getFamilyDetailsByParentId(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    parentId?: number,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response = await this.http.get<FamilyModel[]>(
      `Family/GetFamilyDetailsByParentId`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        parentId: parentId,
      },
      true,
    );
    return response;
  }

  public async removeFamilySpouseKid(
    familyId?: number,
    spouseId?: number,
    kidId?: number,
  ): Promise<FamilyModel> {
    // -- not used
    const response = await this.http.get<FamilyModel>(
      `Family/RemoveFamilySpouseKid`,
      { familyId: familyId, spouseId: spouseId, kidId: kidId },
      false,
    );
    return response;
  }

  public async getFamilyDetailsByParentSpouseId(
    parentId?: string,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response = await this.http.get<FamilyModel[]>(
      `Family/GetFamilyDetailsByParentSpouseId`,
      { parentId: parentId },
      true,
    );
    console.log(response);
    return response;
  }

  public async getFamilyDetailsByFamilyId(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    familyId?: number,
    parentId?: number,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response = await this.http.get<FamilyModel[]>(
      `Family/GetFamilyDetailsByFamilyId`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        familyId: familyId,
        parentId: parentId,
      },
      true,
    );
    return response;
  }

  public async getFamilySummaryByFamilyId(
    familyId?: number,
  ): Promise<FamilyModel[]> {
    // -- not used
    const response = await this.http.get<FamilyModel[]>(
      `Family/GetFamilySummaryByFamilyId`,
      { familyId: familyId },
      true,
    );
    return response;
  }
}
