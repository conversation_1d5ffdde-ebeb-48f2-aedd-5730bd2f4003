import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/dateFormatter.dart';
import 'package:seawork/screens/employee/letterRequest/components/documentListItemDetail.dart';
import 'package:seawork/screens/employee/letterRequest/models/documents_record_model.dart';
import 'package:seawork/utils/style/colors.dart';

class DocumentListItem extends ConsumerWidget {
  // final DocumentResponse documentList;
  final List<DocumentRecord> documentList;
  final int selectedTabIndex;
  final String iconClicked;
  final String? status;
  final String selectedStatus;
  final bool isVisible;
  final bool showDownloadButton;
  final bool hideContainerForScreens;
  final bool removeTopBottomPadding;

  const DocumentListItem({
    Key? key,
    required this.documentList,
    required this.hideContainerForScreens,
    required this.selectedTabIndex,
    required this.selectedStatus,
    required this.iconClicked,
    required this.isVisible,
    required this.showDownloadButton,
    this.status,
    this.removeTopBottomPadding = false,
  }) : super(key: key);

  String formatCreationDate(String isoDateString) {
    try {
      final date = DateTime.parse(isoDateString);

      final monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ];

      final day = date.day;
      final month = monthNames[date.month - 1];
      final year = date.year;

      String getOrdinalSuffix(int day) {
        if (day > 3 && day < 21) return 'th';
        switch (day % 10) {
          case 1:
            return 'st';
          case 2:
            return 'nd';
          case 3:
            return 'rd';
          default:
            return 'th';
        }
      }

      return '$day${getOrdinalSuffix(day)} $month, $year';
    } catch (e) {
      return isoDateString;
    }
  }
 String _toSentenceCase(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: documentList.length,
      itemBuilder: (context, index) {
        final documentListItem = documentList[index];
        bool isTapped = false;
        return StatefulBuilder(
          builder: (context, setState) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              margin: const EdgeInsets.only(left: 12, right: 12, bottom: 8),
              decoration: BoxDecoration(
                color:
                    isTapped
                        ? AppColors.microinteraction
                        : AppColors.whiteColor,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.boxshadowcolor.withOpacity(0.25),
                    offset: const Offset(0, 0),
                    blurRadius: 9.6,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Material(
                color: AppColors.transparentColor,
                borderRadius: BorderRadius.circular(8),
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  splashColor: AppColors.transparentColor,
                  highlightColor: AppColors.transparentColor,
                  onTap: () async {
                    setState(() => isTapped = true);
                    await Future.delayed(const Duration(milliseconds: 200));
                    setState(() => isTapped = false);

                    showModalBottomSheet(
                      backgroundColor: AppColors.transparentColor,
                      context: context,
                      isScrollControlled: true,
                      builder: (context) {
                        return DocumentListItemDetail(documentListItem);
                      },
                    );
                  },
                  child: Padding(
                    padding:
                        removeTopBottomPadding
                            ? const EdgeInsets.only(left: 12, right: 12)
                            : const EdgeInsets.only(
                              left: 12,
                              right: 12,
                              top: 12,
                              bottom: 12,
                            ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Leave Type
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 12.0,
                              ),
                              child: DmSansText(
                                _toSentenceCase(documentListItem.DocumentType),
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.blackColor,
                              ),
                            ),
                            if (!hideContainerForScreens) // Control visibility based on screen
                              Container(
                                height: 22,
                                width: 84,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: const Color.fromARGB(
                                    255,
                                    10,
                                    151,
                                    187,
                                  ), // Light blue background
                                  borderRadius: BorderRadius.circular(25),
                                ),
                                // child: Text(
                                child: OpenSansText(
                                  documentListItem.DocumentType,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.viewColor,
                                ),
                              ),
                          ],
                        ),

                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                CustomSvgImage(imageName: "cardcalender"),
                                const SizedBox(width: 8),
                                OpenSansText(
                                  formatDateWithoutOrdinal(
                                    documentListItem.CreationDate,
                                  ), // Format date here
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.darkGreyColor,
                                ),
                              ],
                            ),
                          ],
                        ),
                        // const SizedBox(height: 4),
                        // const Divider(color: AppColors.lightGreyColor3),
                        // Column(
                        //   crossAxisAlignment: CrossAxisAlignment.start,
                        //   children: [
                        //     buildKeyValueRow(
                        //       "Requested by",
                        //       documentListItem.CreatedBy,
                        //     ),
                        //   ],
                        // ),
                        const SizedBox(height: 12),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Helper Function for Key-Value Display
  Widget buildKeyValueRow(String key, String value, {Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.only(top: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          OpenSansText(
            key,
            fontWeight: FontWeight.bold,
            fontSize: 12,
            color: AppColors.blackColor,
          ),
          SizedBox(
            height: 24,
          ),
          OpenSansText(
            value,
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: textColor ?? AppColors.lightGreyColor,
          ),
        ],
      ),
    );
  }
}
