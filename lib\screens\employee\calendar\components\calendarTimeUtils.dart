import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:seawork/screens/employee/employee/models/meeting.dart';
import 'package:timezone/data/latest.dart' as tz;

class TimeUtils {
  static void initialize() {
    tz.initializeTimeZones();
  }

  // Convert UTC time string to local DateTime
  static DateTime convertToLocalTime(
    String utcTimeString,
    DateTime meetingDate,
  ) {
    try {
      final timeParts = utcTimeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      final utcDateTime = DateTime.utc(
        meetingDate.year,
        meetingDate.month,
        meetingDate.day,
        hour,
        minute,
      );

      return utcDateTime.toLocal();
    } catch (e) {
      debugPrint('Error converting time: $e');
      return meetingDate;
    }
  }

  // Format time for display in local timezone
  static String formatTimeForDisplay(String time, DateTime meetingDate) {
    final localTime = convertToLocalTime(time, meetingDate);
    return DateFormat('h:mm a').format(localTime).toLowerCase();
  }

  static String formatTimeToDisplay(String time, DateTime meetingDate) {
    final localTime = convertToLocalTime(time, meetingDate);
    return DateFormat('hh:mm a').format(localTime).toLowerCase();
  }

  // Calculate end time in local timezone
  static DateTime calculateEndTime(
    String startTimeStr,
    int durationMinutes,
    DateTime meetingDate,
  ) {
    try {
      final localStartTime = convertToLocalTime(startTimeStr, meetingDate);
      return localStartTime.add(Duration(minutes: durationMinutes));
    } catch (e) {
      debugPrint('Error calculating end time: $e');
      return DateTime.now();
    }
  }

  static DateTime getMeetingStartTime(Meeting meeting) {
    try {
      final meetingDate = DateFormat('yyyy-MM-dd').parse(meeting.date);
      return convertToLocalTime(meeting.time, meetingDate);
    } catch (e) {
      debugPrint('Error getting meeting start time: $e');
      return DateTime.now();
    }
  }

  static DateTime getMeetingEndTime(Meeting meeting) {
    try {
      final meetingDate = DateFormat('yyyy-MM-dd').parse(meeting.date);
      return calculateEndTime(
        meeting.time,
        meeting.durationMinutes,
        meetingDate,
      );
    } catch (e) {
      debugPrint('Error getting meeting end time: $e');
      return DateTime.now();
    }
  }

  static bool isMeetingStarted(Meeting meeting) {
    return DateTime.now().isAfter(getMeetingStartTime(meeting));
  }

  static bool isMeetingInProgress(Meeting meeting) {
    final now = DateTime.now();
    return now.isAfter(getMeetingStartTime(meeting)) &&
        now.isBefore(getMeetingEndTime(meeting));
  }

  // Check if meeting has ended (local time)
  static bool isMeetingEnded(Meeting meeting) {
    try {
      final meetingDate = DateFormat('yyyy-MM-dd').parse(meeting.date);
      final endTimeLocal = calculateEndTime(
        meeting.time,
        meeting.durationMinutes,
        meetingDate,
      );
      return DateTime.now().isAfter(endTimeLocal);
    } catch (e) {
      debugPrint('Error checking meeting end status: $e');
      return false;
    }
  }

  // Format date for comparison
  static String formatDateForComparison(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  // Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }
}
