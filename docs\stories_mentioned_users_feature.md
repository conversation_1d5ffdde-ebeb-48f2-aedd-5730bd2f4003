# Stories Mentioned Users Feature

## Overview
This feature displays mentioned users and associated entities next to the role text in story cards. It shows different information based on the user type who created the post.

## Implementation Details

### Location
- **File**: `lib/screens/parent/stories/storiesList.dart`
- **Method**: `_buildMentionedUsers()` in `_StoryCardWidgetState` class
- **UI Location**: Next to the role text in each story card

### Logic Flow

The feature follows this priority order:

1. **Assigned Nurseries** (for admin/staff users)
   - If `CreatedBy.AssignedNurseries` exists and is not empty
   - Shows first nursery name
   - Adds "..." if multiple nurseries exist

2. **Students** (for parent users)
   - If no assigned nurseries but `CreatedBy.Students` exists
   - Shows first student's `FullNameInEnglish`
   - Adds "..." if multiple students exist
   - Filters out empty names

3. **Empty String** (fallback)
   - If neither nurseries nor students exist

### Data Structure

#### For Admin/Staff Users (with AssignedNurseries):
```json
{
  "CreatedBy": {
    "Role": "Super Admin",
    "AssignedNurseries": [
      {
        "Id": 1104,
        "Name": "Al Falah Nursery",
        "NameInArabic": "حضانة الفلاح"
      }
    ]
  }
}
```

#### For Parent Users (with Students):
```json
{
  "CreatedBy": {
    "Role": "Father",
    "Students": [
      {
        "Id": 181,
        "FullNameInEnglish": "Niyaz",
        "FullNameInArabic": "ادم ساجد ساجد فالابيل بيديكايل"
      }
    ]
  }
}
```

### Display Examples

1. **Single Nursery**: `Super Admin • Al Falah Nursery`
2. **Multiple Nurseries**: `Super Admin • Al Falah Nursery...`
3. **Single Student**: `Father • Niyaz`
4. **Multiple Students**: `Father • Niyaz...`
5. **No Data**: `Teacher` (just the role)

### UI Implementation

The mentioned users text is displayed in a `Row` widget next to the role text:

```dart
Row(
  children: [
    OpenSans400Large(10, story.createdBy?.role ?? '', Color(0xFF7B7B7B)),
    SizedBox(width: 4),
    Expanded(
      child: OpenSans400Large(
        10,
        _buildMentionedUsers(story),
        Color(0xFF7B7B7B),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    ),
  ],
)
```

### Key Features

- **Responsive Design**: Uses `Expanded` widget to handle long names
- **Text Overflow**: Ellipsis handling for very long names
- **Priority Logic**: Nurseries take precedence over students
- **Empty Name Filtering**: Ignores students with empty names
- **Consistent Styling**: Matches the role text styling

### Testing

Comprehensive unit tests cover:
- Null safety (when `createdBy` is null)
- Single vs multiple nurseries
- Single vs multiple students
- Priority handling (nurseries over students)
- Empty name filtering
- Edge cases

Run tests with:
```bash
flutter test test/stories_mentioned_users_test.dart
```

## Technical Concepts Used

### Flutter Widgets
- **Row**: Horizontal layout for role and mentioned users
- **Expanded**: Responsive width allocation
- **SizedBox**: Spacing between elements

### Dart Programming
- **Null Safety**: Safe navigation with `?.` operator
- **List Operations**: `map()`, `where()`, `isNotEmpty`
- **String Interpolation**: Building display strings
- **Conditional Logic**: Priority-based display logic

### Provider Pattern
- **State Management**: Uses existing `StoriesProvider`
- **Data Access**: Accesses story data through provider
- **Reactive UI**: Updates when story data changes

This implementation provides a clean, efficient way to display contextual user information in story cards while maintaining good performance and user experience.
