import 'package:flutter_test/flutter_test.dart';
import 'package:seawork/screens/parent/stories/model/storiesListModel.dart';

void main() {
  group('Mentioned Users Functionality Tests', () {
    test('should return empty string when createdBy is null', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: null,
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, '');
    });

    test('should show single assigned nursery for admin users', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 1,
          name: 'Admin User',
          role: 'Super Admin',
          assignedNurseries: [
            AssignedNursery(
              id: 1104,
              name: 'Al Falah Nursery',
              nameInArabic: 'حضانة الفلاح',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Al Falah Nursery');
    });

    test('should show first nursery with ellipsis for multiple assigned nurseries', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 1,
          name: 'Admin User',
          role: 'Super Admin',
          assignedNurseries: [
            AssignedNursery(
              id: 1104,
              name: 'Al Falah Nursery',
              nameInArabic: 'حضانة الفلاح',
            ),
            AssignedNursery(
              id: 1105,
              name: 'Al Rahmaniya Nursery',
              nameInArabic: 'حضانة الرحمانية',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Al Falah Nursery...');
    });

    test('should show single student for parent users', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 784198774033027,
          name: 'Asad Afreen',
          role: 'Father',
          students: [
            Student(
              id: 181,
              fullNameInEnglish: 'Niyaz',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Niyaz');
    });

    test('should show first student with ellipsis for multiple students', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 784198774033027,
          name: 'Asad Afreen',
          role: 'Father',
          students: [
            Student(
              id: 181,
              fullNameInEnglish: 'Niyaz',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
            Student(
              id: 182,
              fullNameInEnglish: 'Livin',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Niyaz...');
    });

    test('should prioritize assigned nurseries over students', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 1,
          name: 'Admin User',
          role: 'Super Admin',
          assignedNurseries: [
            AssignedNursery(
              id: 1104,
              name: 'Al Falah Nursery',
              nameInArabic: 'حضانة الفلاح',
            ),
          ],
          students: [
            Student(
              id: 181,
              fullNameInEnglish: 'Niyaz',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Al Falah Nursery');
    });

    test('should return empty string when no nurseries or students', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 1,
          name: 'User',
          role: 'Teacher',
          assignedNurseries: [],
          students: [],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, '');
    });

    test('should handle empty student names', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 784198774033027,
          name: 'Parent User',
          role: 'Father',
          students: [
            Student(
              id: 181,
              fullNameInEnglish: '',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
            Student(
              id: 182,
              fullNameInEnglish: 'Livin',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Livin');
    });
  });
}

// Helper function extracted from the main implementation for testing
String _buildMentionedUsers(StoriesListModel story) {
  if (story.createdBy == null) return '';
  
  final createdBy = story.createdBy!;
  
  // Check if there are assigned nurseries (for admin/staff users)
  if (createdBy.assignedNurseries != null && createdBy.assignedNurseries!.isNotEmpty) {
    if (createdBy.assignedNurseries!.length == 1) {
      return ' • ${createdBy.assignedNurseries!.first.name ?? ''}';
    } else {
      return ' • ${createdBy.assignedNurseries!.first.name ?? ''}...';
    }
  }
  
  // Check if there are students (for parent users)
  if (createdBy.students != null && createdBy.students!.isNotEmpty) {
    final studentNames = createdBy.students!
        .map((student) => student.fullNameInEnglish ?? '')
        .where((name) => name.isNotEmpty)
        .toList();
    
    if (studentNames.isEmpty) return '';
    
    if (studentNames.length == 1) {
      return ' • ${studentNames.first}';
    } else {
      return ' • ${studentNames.first}...';
    }
  }
  
  return '';
}
