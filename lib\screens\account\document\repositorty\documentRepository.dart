
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:intl/intl.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/account/Document/model/documentModel.dart';

// Dio Provider
final dioProvider = Provider<Dio>((ref) {
  return _createDio();
});

Dio _createDio() {
  Dio dio = Dio();

  if (NetworkModule.currentEnvironment == Environment.dev && !kIsWeb) {
    print('Disabling SSL certificate verification for development environment');
    dio.httpClientAdapter = IOHttpClientAdapter(createHttpClient: () {
      final client = HttpClient();
      client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      return client;
    });
  }

  return dio;
}

// Document Repository Provider
final documentRepositoryProvider = Provider<DocumentRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return DocumentRepository(dio);
});

class DocumentRepository {
  final Dio _dio;

  DocumentRepository(this._dio);

  Future<MyDocumentModel> getApplicationDocumentsDirectory({
    String? documentType,
    String? status,
    String? filterType,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      const int personId = 300000015297106;
      const int limit = 100;
      int offset = 0;
      final List<Item> allItems = [];

      // Initial request
      final firstResponse = await _getPaginatedRequest(
        personId: personId,
        limit: limit,
        offset: offset,
        documentType: documentType,
        status: status,
        filterType: filterType,
        searchQuery: searchQuery,
        startDate: startDate,
        endDate: endDate,
      );

      if (firstResponse.statusCode != 200 && firstResponse.statusCode != 201) {
        throw Exception('Failed to fetch initial document page');
      }

      final firstPage = MyDocumentModel.fromJson(firstResponse.data);
      allItems.addAll(firstPage.items);

      // If there are more items to fetch
      if (firstPage.hasMore) {
        final totalPages = (firstPage.totalResults / limit).ceil();
        final List<Future<Response>> requests = [];

        for (int i = 1; i < totalPages; i++) {
          offset = i * limit;
          requests.add(_getPaginatedRequest(
            personId: personId,
            limit: limit,
            offset: offset,
            documentType: documentType,
            status: status,
            filterType: filterType,
            searchQuery: searchQuery,
            startDate: startDate,
            endDate: endDate,
          ));
        }

        // Execute all in parallel
        final responses = await Future.wait(requests);

        for (final response in responses) {
          if (response.statusCode == 200 || response.statusCode == 201) {
            final page = MyDocumentModel.fromJson(response.data);
            allItems.addAll(page.items);
          } else {
            print('Failed to fetch one page: ${response.statusCode}');
          }
        }
      }

      return MyDocumentModel(
        items: allItems,
        totalResults: allItems.length,
        count: allItems.length,
        hasMore: false,
        limit: limit,
        offset: offset,
        links: [],
      );
    } catch (e, stackTrace) {
      print('Error fetching document: $e');
      print('StackTrace: $stackTrace');
      return MyDocumentModel(
        items: [],
        totalResults: 0,
        count: 0,
        hasMore: false,
        limit: 0,
        offset: 0,
        links: [],
      );
    }
  }

  Future<Response> _getPaginatedRequest({
    required int personId,
    required int limit,
    required int offset,
    String? documentType,
    String? status,
    String? filterType,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final baseQuery = 'PersonId=$personId';
    var query = baseQuery;

    if (documentType != null) {
      query += ';DocumentType="$documentType"';
    }

    final queryParameters = {
      'q': query,
      'onlyData': 'true',
      'expand': 'all',
      'limit': limit.toString(),
      'offset': offset.toString(),
      'orderBy': 'CreationDate:desc',
    };

    // Add status filter if provided
    if (status != null) {
      queryParameters['status'] = status;
    }

    // Add filter type conditions
    if (filterType != null) {
      switch (filterType) {
        case 'Open tickets':
          queryParameters['status_id'] = '1';
          break;
        case 'Closed tickets':
          queryParameters['status_id'] = '9';
          break;
        case 'Awaiting tickets':
          queryParameters['awaitinginput'] = '1';
          break;
        // 'All tickets' case doesn't need additional parameters
      }
    }

    // Add search query if provided
    if (searchQuery != null && searchQuery.isNotEmpty) {
      queryParameters['search'] = searchQuery;
    }

    // Add date filters if provided
    if (startDate != null) {
      queryParameters['startDate'] = DateFormat('yyyy-MM-dd').format(startDate);
    }
    if (endDate != null) {
      queryParameters['endDate'] = DateFormat('yyyy-MM-dd').format(endDate);
    }

    return _dio.get(
      '${NetworkModule.baseUrlEMS}/documentRecords',
      queryParameters: queryParameters,
    );
  }
}

// State Notifier for Document Management
class DocumentNotifier extends StateNotifier<AsyncValue<MyDocumentModel>> {
  final Ref ref;

  DocumentNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadDocuments();
  }

  Future<void> loadDocuments({
    String? documentType,
    String? status,
    String? filterType,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    state = const AsyncValue.loading();
    try {
      final repository = ref.read(documentRepositoryProvider);
      final documents = await repository.getApplicationDocumentsDirectory(
        documentType: documentType,
        status: status,
        filterType: filterType,
        searchQuery: searchQuery,
        startDate: startDate,
        endDate: endDate,
      );
      state = AsyncValue.data(documents);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

// Document Notifier Provider
final documentNotifierProvider = StateNotifierProvider<DocumentNotifier, AsyncValue<MyDocumentModel>>((ref) {
  return DocumentNotifier(ref);
});

// Filter Provider
final documentFilterProvider = StateProvider<Map<String, dynamic>>((ref) => {});

// Helper function to get filtered documents
final filteredDocumentsProvider = Provider<AsyncValue<MyDocumentModel>>((ref) {
  final filters = ref.watch(documentFilterProvider);
  final documents = ref.watch(documentNotifierProvider);

  return documents.when(
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
    data: (data) {
      // Apply additional filtering if needed
      return AsyncValue.data(data);
    },
  );
});