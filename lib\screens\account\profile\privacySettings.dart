import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart'
    as customNav;
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/screens/account/profile/biometricPopUp.dart';
import 'package:seawork/utils/style/colors.dart';

class PrivacySettingsScreen extends ConsumerStatefulWidget {
  const PrivacySettingsScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<PrivacySettingsScreen> createState() =>
      _PrivacySettingsScreenState();
}

class _PrivacySettingsScreenState extends ConsumerState<PrivacySettingsScreen> {
  bool _isSwitchEnabled = false;
  bool _isBiometricEnabled = false;
  bool _showLockOptions = false;
  int _selectedLockOption = 0;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      BiometricPopup.show(context, () {
        setState(() {
          _isSwitchEnabled = true;
        });
      });
    });
  }

  void _handleBiometricSuccess() {
    setState(() {
      _isSwitchEnabled = true;
      _showLockOptions = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(title: 'Privacy settings', showActionIcon: true),
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 24,
              child: OpenSansText(
                "App lock",
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: AppColors.blackColor,
              ),
            ),
            const SizedBox(height: 24),
            _buildBiometricSwitch(),
            if (_showLockOptions) _buildLockOptions(),
          ],
        ),
      ),
      bottomNavigationBar: customNav.CustomBottomNavigationBar(
        onTap: (index) {
          ref.read(customNav.bottomNavBarProvider.notifier).state = index;
        },
      ),
    );
  }

  /// Builds the biometric switch section
  Widget _buildBiometricSwitch() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              OpenSansText(
                "Unlock with biometric",
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppColors.blackColor,
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  OpenSansText(
                    "When enabled, you'll need to use fingerprint,\nface or other unique identifiers to open",
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.dimColor,
                  ),
                  SizedBox(
                    width: 41,
                    height: 22,
                    child: Transform.scale(
                      scale: 22 / 31,
                      child: CupertinoSwitch(
                        value: _isBiometricEnabled,
                        onChanged: (value) {
                          setState(() {
                            _isBiometricEnabled = value;
                            _showLockOptions = value;
                          });
                        },
                        activeColor: AppColors.lightGreyColor2,
                        trackColor: AppColors.lightGreyColor2,
                        thumbColor:
                            _isBiometricEnabled
                                ? AppColors.viewColor
                                : AppColors.whiteColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        //
      ],
    );
  }

  /// Builds the lock options section
  Widget _buildLockOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 20),
        const Divider(thickness: 1, color: AppColors.editingcolor),
        const SizedBox(height: 16),
        OpenSansText(
          "Automatically lock",
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppColors.dimColor,
        ),
        const SizedBox(height: 20),
        _buildLockOption("Immediately", 0),
        const SizedBox(height: 20),
        _buildLockOption("After 1 minute", 1),
        const SizedBox(height: 20),
        _buildLockOption("After 30 minutes", 2),
      ],
    );
  }

  /// Builds each lock option
  Widget _buildLockOption(String title, int value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Radio<int>(
          value: value,
          groupValue: _selectedLockOption,
          activeColor: AppColors.viewColor,
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          visualDensity: VisualDensity(horizontal: -4, vertical: -4),
          onChanged: (int? newValue) {
            setState(() {
              _selectedLockOption = newValue!;
            });
          },
        ),
        const SizedBox(width: 4),
        OpenSansText(
          title,
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: AppColors.dimColor,
        ),
      ],
    );
  }
}
