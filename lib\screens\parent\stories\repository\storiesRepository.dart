import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';
import '../model/storiesListModel.dart';
import '../model/reaction_model.dart';
import 'package:flutter/foundation.dart';

class StoriesRepository {
  final String presignedUrlEndpoint = 'https://apilooppe.sn.ac.ae/gateway/upload/GeneratePresignedUrlForDownload?IsThumbNail=false&IsCertificate=false';
  final String bearerToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6Ijc4NDE5ODc3NDAzMjg5OCIsInJvbGUiOiIwIiwibmJmIjoxNzUxNDYxMzM4LCJleHAiOjE3NTE1NDc3MzgsImlhdCI6MTc1MTQ2MTMzOH0.xzlcjeHWwBvhVIuvcNWNpnk9Wx7G7SAqKUx0sle0IWs';

  final Dio dio;
  final String baseUrl;

  StoriesRepository({required this.baseUrl}) : dio = _createDio(baseUrl);

  static Dio _createDio(String baseUrl) {
    Dio dio = Dio();
    dio.options.baseUrl = baseUrl;
    dio.options.connectTimeout = const Duration(minutes: 2);
    dio.options.receiveTimeout = const Duration(minutes: 2);
    dio.options.headers = {'Content-Type': 'application/json; charset=utf-8'};
    return dio;
  }

  Future<List<StoriesListModel>> getAllPosts({int page = 1}) async {
    try {
      final response = await dio.get('/post/getAllPosts', queryParameters: {
        'pageNumber': page,
        'pageSize': 10,
      });
      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;
        return StoriesListModel.listFromJson(data);
      } else {
        throw Exception('Failed to load posts');
      }
    } catch (e) {
      throw Exception('Failed to load posts: $e');
    }
  }

  // ↓ put this right under getAllPosts()
  Future<List<StoriesListModel>> getAllPostsForYou({
    required int pageNumber,
    required int pageSize,
    required String orderBy,
    required String userId,
    required int userType,
  }) async {
    try {
      final res = await dio.get(
        '/post/GetAllPostsForYou',
        queryParameters: {
          'pageNumber': pageNumber,
          'pageSize'  : pageSize,
          'orderBy'   : orderBy,
          'userId'    : userId,
          'userType'  : userType,
        },
      );
      if (res.statusCode == 200 && res.data is List) {
        return StoriesListModel.listFromJson(res.data as List);
      }
      throw Exception('Unexpected response');
    } catch (e) {
      debugPrint('getAllPostsForYou failed: $e');
      rethrow;
    }
  }

  Future<String?> getPresignedUrl(String key) async {
    if (key.isEmpty) return null;
    final url = Uri.parse(presignedUrlEndpoint);
    // The API expects the body as a raw JSON string, e.g. "thumbnailkey.png"
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $bearerToken',
      },
      body: jsonEncode(key), // This produces a raw JSON string, not an object
    );
    if (response.statusCode == 200) {
      return response.body; // Use plain text response
    } else {
      return null;
    }
  }

  Future<bool> addOrUpdatePostReaction({
    required int postId,
    required int userType,
    required int reactionType,
    String? imageKey, // NEW: optional image key for presigned URL
  }) async {
    try {
      // Get presigned URL if imageKey is provided
      String? postImageUrl;
      if (imageKey != null && imageKey.isNotEmpty) {
        postImageUrl = await fetchPresignedUrl(imageKey); // Use new method
      }

      final res = await dio.post(
        '$baseUrl/post/AddUpdatePostReaction',
        data: {
          'postId'      : postId,
          'userType'    : userType,
          'reactionType': reactionType,        // same value again = un-like
          if (postImageUrl != null) 'postImageUrl': postImageUrl, // NEW: include presigned URL
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $bearerToken',
          },
        ),
      );
      return res.statusCode == 200;
    } catch (e) {
      debugPrint('❌  react API failed: $e');
      return false;
    }
  }

  //---------------------------------------------------------------------------
  // new: fetch reactions for a post (paged)
  //---------------------------------------------------------------------------
  Future<List<ReactionEntry>> getReactionsByPostId({
    required int postId,
    required int userId,
    required int userType,
    int page      = 1,
    int pageSize  = 10,
    String order  = 'desc',
  }) async {
    try {
      final res = await dio.get(
        '/post/GetReactionsByPostId',
        queryParameters: {
          'pageNumber': page,
          'pageSize'  : pageSize,
          'orderBy'   : order,
          'userId'    : userId,
          'userType'  : userType,
          'postId'    : postId,
        },
      );
      if (res.statusCode == 200 && res.data is List) {
        return ReactionEntry.listFromJson(res.data);
      } else {
        return [];
      }
    } catch (e) {
      debugPrint('❌ getReactionsByPostId failed: $e');
      rethrow;
    }
  }

  // ─────────────────────────────────────────────────────────────
  //  FLAG / REPORT  ►  POST  /post/AddOrRemovePostFlags
  // ─────────────────────────────────────────────────────────────
  Future<bool> addOrRemovePostFlags({
    required int postId,
    required int userId,
    required int userType,
    required int reasonType,      // 1 = "Inappropriate content", 2 = …
    required String description,  // remarks from the bottom-sheet
    String? imageKey, // NEW: optional image key for presigned URL
  }) async {
    try {
      // Get presigned URL if imageKey is provided
      String? postImageUrl;
      if (imageKey != null && imageKey.isNotEmpty) {
        postImageUrl = await fetchPresignedUrl(imageKey); // Use new method
      }

      final res = await dio.post(
        '/post/AddOrRemovePostFlags',
        data: {
          'postId'     : postId,
          'userId'     : userId,
          'userType'   : userType,
          'reasonType' : reasonType,
          'description': description,
          if (postImageUrl != null) 'postImageUrl': postImageUrl, // NEW: include presigned URL
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $bearerToken',
          },
        ),
      );
      return res.statusCode == 200;          // back-end returns the object
    } catch (e) {
      debugPrint('❌  flag API failed: $e');
      return false;
    }
  }
}

// ───────── NEW EXTENSION FOR PRESIGNED URL ─────────
// KEEP the old getPresignedUrl() for now – you will delete later.
// -----------------------------------------------------------------
// NEW network call that hits the NestJS wrapper created above.
// -----------------------------------------------------------------
// ─────────────────────────────────────────────
// ADD at the bottom of storiesRepository.dart
// ─────────────────────────────────────────────

class PostViewPayload {
  final int postId;
  final int userId;
  final int userType;

  const PostViewPayload({
    required this.postId,
    required this.userId,
    required this.userType,
  });

  Map<String, dynamic> toJson() => {
        'postId': postId,
        'userId': userId,
        'userType': userType,
      };
}

extension PostViewsApi on StoriesRepository {
  /// POST  /post/AddMultiplePostViews
  ///
  /// • Accepts between 1-N view objects.
  /// • Returns plain-text `"Success"` on OK.
  Future<bool> addMultiplePostViews(
    List<PostViewPayload> payload,
  ) async {
    if (payload.isEmpty) return true; // nothing to do

    try {
      final res = await dio.post(
        '/post/AddMultiplePostViews',
        data: payload.map((e) => e.toJson()).toList(),
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            // 'Authorization': 'Bearer $bearerToken',
          },
        ),
      );
      return res.statusCode == 200 && res.data == 'Success';
    } catch (e) {
      debugPrint('addMultiplePostViews failed: $e');
      return false;
    }
  }
}

extension Presigned on StoriesRepository {
  Future<String?> fetchPresignedUrl(String fileKey) async {
    if (fileKey.isEmpty) return null;
    try {
      debugPrint('🔍 Attempting to fetch presigned URL for: $fileKey');
      debugPrint('🔍 Base URL: ${dio.options.baseUrl}');
      debugPrint('🔍 Full URL: ${dio.options.baseUrl}/post/generatePresignedUrlForDownload');

      // Send fileKey as a JSON object with "fileKey" property as shown in Postman test
      final res = await dio.post(
        '/post/generatePresignedUrlForDownload',
        data: {
          "fileKey": fileKey
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Connection': 'keep-alive',
          },
        ),
      );
      debugPrint('✅ Response status: ${res.statusCode}');
      debugPrint('✅ Response data: ${res.data}');
      
      // Accept both 200 and 201 status codes
      if (res.statusCode == 200 || res.statusCode == 201) {
        // Check if the URL contains "[object Object]" which indicates a backend issue
        String url = res.data as String;
        if (url.contains('[object%20Object]')) {
          // Try to fix the URL by replacing the problematic part with the actual fileKey
          url = url.replaceAll('[object%20Object]', fileKey);
          debugPrint('🔧 Fixed URL: $url');
        }
        return url;
      }
      return null;
    } catch (e) {
      debugPrint('❌ fetchPresignedUrl failed: $e');
      if (e is DioException) {
        debugPrint('❌ Request URL: ${e.requestOptions.uri}');
        debugPrint('❌ Request data: ${e.requestOptions.data}');
        debugPrint('❌ Request headers: ${e.requestOptions.headers}');
        debugPrint('❌ Response status: ${e.response?.statusCode}');
        debugPrint('❌ Response data: ${e.response?.data}');
      }
      return null;
    }
  }

  // ───────── TESTING HELPER ─────────
  // Use this to test the new endpoint:
  // final url = await _storiesRepository.fetchPresignedUrl('my/image/key.jpg');
  // print(url); // you should see an S3 style URL with signatures
  // ─────────────────────────────────
}
