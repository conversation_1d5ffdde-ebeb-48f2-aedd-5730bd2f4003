import 'dart:async';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:seawork/firebaseOptions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/screens/dashboard/mainDashboard/mainDashboard.dart';
import 'package:seawork/screens/parent/ParentSplash/components/initialScreen.dart';
import 'package:seawork/screens/public/login/login.dart';
import 'package:seawork/data/preferencesUtils.dart';
import 'package:seawork/screens/service/ticket/help.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

void main() async {
  HttpOverrides.global = MyHttpOverrides();
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  runApp(ProviderScope(child: MyAppWrapper()));
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  Key _key = UniqueKey();
  StreamSubscription<Uri?>? _sub;

  void refresh() {
    print('Refreshing app state...');
    setState(() {
      _key = UniqueKey();
    });
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _sub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return KeyedSubtree(
      key: _key,
      child: MaterialApp(
        scaffoldMessengerKey: scaffoldMessengerKey,
        navigatorKey: navigatorKey,
        debugShowCheckedModeBanner: false,
        theme: ThemeData(primarySwatch: Colors.blue),
        home: FutureBuilder(
          future: PreferencesUtils.getString(PreferencesUtils.SESSION_TOKEN),
          builder: (context, snapshot) {
            print('[Main] Session check: ${snapshot.data}');
            if (snapshot.connectionState == ConnectionState.done) {
              if (snapshot.hasData) {
                print('Valid session found, routing to Dashboard');
                return MainDashboard();
              } else {
                print('No session found, showing Login');
                // return kIsWeb ? Login() : MainDashboard;
                // return Login();
                return InitialScreen();
              }
            }
            return Scaffold(body: Center(child: CustomLoadingWidget()));
          },
        ),
        routes: {
          '/login': (context) => Login(),
          '/oauthredirect': (context) => Login(),
          '/dashboard': (context) => MainDashboard(),
          'help': (context) => Help(iconClicked: 'Help'),
        },
      ),
    );
  }
}

class MyAppWrapper extends StatefulWidget {
  @override
  State<MyAppWrapper> createState() => _MyAppWrapperState();
}

class _MyAppWrapperState extends State<MyAppWrapper> {
  @override
  Widget build(BuildContext context) {
    return MyApp();
  }
}
