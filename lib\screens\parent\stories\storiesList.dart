import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/box/container.dart';
import 'package:seawork/screens/parent/stories/components/storiesbottomnavigation.dart';
import 'package:seawork/screens/parent/stories/provider/storiesProvider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:seawork/screens/parent/stories/model/storiesListModel.dart';
import 'package:seawork/screens/parent/stories/components/reaction_bottom_sheet.dart';
import 'package:seawork/screens/parent/stories/components/report_bottom_sheet.dart';
import 'package:visibility_detector/visibility_detector.dart';

class StoriesListScreen extends StatefulWidget {
  const StoriesListScreen({Key? key}) : super(key: key);

  @override
  State<StoriesListScreen> createState() => _StoriesListScreenState();
}

class _StoriesListScreenState extends State<StoriesListScreen> {
  late final StoriesProvider _provider;          // ★ keep a ref
  final ScrollController _scrollController = ScrollController();

  // Track popup close trigger for all cards
  int popupCloseTrigger = 0;

  @override
  void initState() {
    super.initState();
    
    // Initialize the provider
    _provider = StoriesProvider(userId: '784198774032898');
    
    // ★ on first load default to "All posts"
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _provider.fetchPosts(refresh: true, forYou: false);
    });
    
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (_provider.hasMore && !_provider.isLoading) {
        // ★ when we hit the bottom keep loading the SAME feed (all-posts OR for-you)
        _provider.fetchPosts(forYou: _provider.isForYou);
      }
    }
    // Close all popups on any scroll
    setState(() {
      popupCloseTrigger++;
    });
  }

  void closeAllPopups() {
    setState(() {
      popupCloseTrigger++;
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<StoriesProvider>.value(
      value: _provider,
      child: Consumer<StoriesProvider>(
        builder: (context, provider, _) {
          return SecondaryScaffoldWithAppBar(
            context,
            'Stories',
            // Back icon (hidden for main stories screen)
            SizedBox(width: 24),
            null,
            showHelpIcon: true,
            bodyItem: Column(
              children: [
                SizedBox(height: 16),
                // Welcome Container
                Container(
                  width: 353,
                  height: 148,
                  margin: EdgeInsets.symmetric(horizontal: 20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Stack(
                    children: [
                      // Welcome Text
                      Positioned(
                        top: 16,
                        left: 20,
                        child: OpenSans600Large(
                          14,
                          'Welcome to your story corner!',
                          Colors.black,
                        ),
                      ),
                      // Subtitle
                      Positioned(
                        top: 39,
                        left: 20,
                        child: OpenSans400Large(
                          10,
                          'Stay connected through shared moments',
                          Color(0xFF605E5E),
                        ),
                      ),
                      // Divider
                      Positioned(
                        top: 64,
                        left: 20,
                        child: Container(
                          width: 313,
                          height: 1,
                          color: Color(0xFFD2E4F1),
                        ),
                      ),
                      // Images (placeholder for now)
                      Positioned(
                        top: 76,
                        left: 20,
                        child: Row(
                          children: [
                            _storyImage('', index: 0),
                            SizedBox(width: 8),
                            _storyImage('', index: 1),
                            SizedBox(width: 8),
                            _storyImage('', index: 2),
                          ],
                        ),
                      ),
                      // Saved Draft Button
                      Positioned(
                        top: 87,
                        left: 228,
                        child: Container(
                          width: 105,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Color(0xFFE6F4FF),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                'assets/images/saved_drafts.svg',
                                width: 18,
                                height: 18,
                              ),
                              SizedBox(width: 6),
                              OpenSans600Large(12, 'Saved draft', AppColors.viewColor),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16),
                // Stories List
                Expanded(
                  child: provider.isLoading && provider.posts.isEmpty
                      ? Center(child: CircularProgressIndicator())
                      : provider.posts.isEmpty
                          ? Center(child: Text('No stories found'))
                          : RefreshIndicator(
                              onRefresh: () async {
                                await provider.fetchPosts(refresh: true, forYou: provider.isForYou);
                              },
                              child: ListView.builder(
                                controller: _scrollController,
                                padding: EdgeInsets.symmetric(horizontal: 20),
                                itemCount: context.watch<StoriesProvider>().posts.length + (provider.hasMore ? 1 : 0),
                                itemBuilder: (context, index) {
                                  if (index == provider.posts.length) {
                                    // Show loading indicator at the end for pagination
                                    return Center(child: Padding(
                                      padding: EdgeInsets.symmetric(vertical: 16),
                                      child: CircularProgressIndicator(),
                                    ));
                                  }

                                  // Use Selector to rebuild only when this specific post changes
                                  return Selector<StoriesProvider, StoriesListModel>(
                                    selector: (_, p) => p.posts[index],
                                    builder: (_, story, __) => VisibilityDetector(
                                      key: ValueKey('post-${story.id}'),
                                      onVisibilityChanged: (info) {
                                        // Fire when ≥50% of the card is in the viewport
                                        if (info.visibleFraction >= 0.5) {
                                          context.read<StoriesProvider>().markPostViewed(story.id!);
                                        }
                                      },
                                      child: StoryCardWidget(
                                        key: ValueKey(story.id),  // Add key for better widget reconciliation
                                        postId: story.id!,
                                        closeAllPopups: closeAllPopups,
                                        popupCloseTrigger: popupCloseTrigger,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                ),
              ],
            ),
            bottomNavigationBar: storiesBottomNavigationBar(
              onTap: (index) {},
            ),
          );
        },
      ),
    );
  }

  Widget _storyImage(String url, {int index = 0}) {
    // Use the three provided image URLs
    final List<String> images = [
      'https://marketing.create-cdn.net/assets/freeimages_photo.jpg',
      'https://i0.wp.com/picjumbo.com/wp-content/uploads/beautiful-nature-mountain-scenery-with-flowers-free-photo.jpg?w=2210&quality=70',
      'https://www.pixelstalk.net/wp-content/uploads/2016/07/3840x2160-Images-Free-Download.jpg',
    ];
    final imageUrl = images[index % images.length];
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.network(
        imageUrl,
        width: 65,
        height: 52,
        fit: BoxFit.cover,
      ),
    );
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) {
      return 'Unknown date';
    }
    final now = DateTime.now();
    final diff = now.difference(dateTime);
    if (diff.inMinutes < 60) {
      return '${diff.inMinutes} minutes ago';
    } else if (diff.inHours < 24) {
      return '${diff.inHours} hours ago';
    } else {
      return '${diff.inDays} days ago';
    }
  }
}

// New StatefulWidget for each story card
class StoryCardWidget extends StatefulWidget {
  final int postId;
  final VoidCallback? closeAllPopups;
  final int popupCloseTrigger;
  const StoryCardWidget({
    Key? key,
    required this.postId,
    this.closeAllPopups,
    this.popupCloseTrigger = 0,
  }) : super(key: key);

  @override
  State<StoryCardWidget> createState() => _StoryCardWidgetState();
}

class _StoryCardWidgetState extends State<StoryCardWidget> {
  bool showPopup = false;
  int lastPopupCloseTrigger = 0;

  String? profilePicUrl;
  String? postImageUrl;
  String? lastProfileKey;
  String? lastPostImageKey;
  bool profileLoading = false;
  bool postImageLoading = false;

  // For popup positioning
  final GlobalKey _reactionButtonKey = GlobalKey();

  @override
  void didUpdateWidget(covariant StoryCardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (showPopup && lastPopupCloseTrigger != widget.popupCloseTrigger) {
      setState(() {
        showPopup = false;
        lastPopupCloseTrigger = widget.popupCloseTrigger;
      });
    } else {
      lastPopupCloseTrigger = widget.popupCloseTrigger;
    }
  }

  Future<void> _fetchProfilePic(String? key) async {
    if (key == null || key.isEmpty) {
      setState(() { profilePicUrl = null; profileLoading = false; });
      return;
    }
    if (lastProfileKey == key && profilePicUrl != null) return;
    setState(() { profileLoading = true; });
    final provider = Provider.of<StoriesProvider>(context, listen: false);
    final url = await provider.getPresignedUrlNew(key);
    setState(() {
      profilePicUrl = url;
      lastProfileKey = key;
      profileLoading = false;
    });
  }

  Future<void> _fetchPostImage(String? key) async {
    if (key == null || key.isEmpty) {
      setState(() { postImageUrl = null; postImageLoading = false; });
      return;
    }
    if (lastPostImageKey == key && postImageUrl != null) return;
    setState(() { postImageLoading = true; });
    final provider = Provider.of<StoriesProvider>(context, listen: false);
    final url = await provider.getPresignedUrlNew(key);
    setState(() {
      postImageUrl = url;
      lastPostImageKey = key;
      postImageLoading = false;
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final provider = Provider.of<StoriesProvider>(context, listen: false);
    final story = provider.posts.firstWhere((p) => p.id == widget.postId);
    _fetchProfilePic(story.createdBy?.profileDocumentUpload);
    if (story.postAttachments != null && story.postAttachments!.isNotEmpty) {
      _fetchPostImage(story.postAttachments![0].attachmentKey);
    }
  }

  // Helper to map reactionType → asset
  String getReactionAsset(int reactionType) {
    switch (reactionType) {
      case ReactionType.thumbsup: return 'assets/images/thumbsup_stories.svg';
      case ReactionType.heart   : return 'assets/images/heart_stories_red.svg';
      case ReactionType.clap    : return 'assets/images/clap_stories.svg';
      case ReactionType.smile   : return 'assets/images/smile_stories.svg';
      case ReactionType.none:      // <-- explicit NONE state
      default               : return 'assets/images/heart_icon_stories.svg'; // 🔹 correct path
    }
  }

  // Helper to handle single tap on reaction icon
  void _handleSingleTap(StoriesListModel story) {
    final prov = context.read<StoriesProvider>();

    final int reactionTypeToSend =
        (story.userReaction == null ||   // → post is NOT reacted
         story.userReaction!.reactionType == null ||
         story.userReaction!.reactionType == ReactionType.none)
            ? ReactionType.heart          // 1st tap = like with red heart
            : story.userReaction!.reactionType!; // 2nd tap = send same → un-like

    prov.reactToPost(
      postId: story.id!,
      reactionType: reactionTypeToSend,
      imageKey: story.postAttachments?.isNotEmpty == true
          ? story.postAttachments!.first.attachmentKey
          : null, // NEW: pass image key if available
    );
  }

  @override
  Widget build(BuildContext context) {
    final provider = context.watch<StoriesProvider>();
    final story = provider.posts.firstWhere((p) => p.id == widget.postId);
    final int? userReactionType = story.userReaction?.reactionType ?? ReactionType.none;
    final int totalReactions = story.reactionCount ?? 0;

    Widget cardContent = Container(
      width: 353,
      margin: EdgeInsets.only(bottom: 16, left: 20, right: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Color(0x6680CAD6),
            blurRadius: 9.6,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(width: 12),
              // Profile Image (cached)
              profileLoading
                  ? SizedBox(
                      width: 40,
                      height: 40,
                      child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
                    )
                  : ClipRRect(
                      borderRadius: BorderRadius.circular(120),
                      child: profilePicUrl == null
                          ? Image.asset(
                              'assets/images/stories_profile.png',
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                            )
                          : Image.network(
                              profilePicUrl!,
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                            ),
                    ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    OpenSans600Large(
                      12,
                      story.createdBy?.name ?? '',
                      Colors.black,
                    ),
                    SizedBox(height: 3),
                    OpenSans400Large(
                      10,
                      story.createdBy?.role ?? '',
                      Color(0xFF7B7B7B),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 24,
                height: 24,
                child: Icon(Icons.more_vert, color: Color(0xFF7B7B7B)),
              ),
              SizedBox(width: 12),
            ],
          ),
          SizedBox(height: 12),
          if (story.postAttachments?.isNotEmpty == true && story.postAttachments![0].attachmentKey != null)
            postImageLoading
                ? Container(
                    width: 329,
                    height: 186,
                    margin: EdgeInsets.only(left: 12, right: 12),
                    alignment: Alignment.center,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : (postImageUrl != null && postImageUrl!.isNotEmpty)
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          postImageUrl!,
                          width: 329,
                          height: 186,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            debugPrint('❌ Error loading image: $error');
                            debugPrint('❌ Image URL: $postImageUrl');
                            return Container(
                              width: 329,
                              height: 186,
                              color: Colors.grey[300],
                              child: Center(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(Icons.error, color: Colors.red),
                                    SizedBox(height: 8),
                                    Text('Failed to load image', style: TextStyle(color: Colors.red)),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      )
                    : SizedBox.shrink(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: OpenSans400Large(
              12,
              story.postBody ?? '',
              Colors.black,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: GestureDetector(
              onTap: () => showReactionBottomSheet(context, story.id!),
              child: Row(
                children: [
                  // Show only icons with count > 0 from reactionCountsByGroup
                  ..._buildActiveReactions(story.reactionCountsByGroup),
                  SizedBox(width: 2),
                  // Optionally, show total reaction count if you want
                  // Text(
                  //   totalReactions.toString(),
                  //   style: GoogleFonts.openSans(
                  //     fontSize: 12,
                  //     fontWeight: FontWeight.w400,
                  //     color: Colors.black,
                  //   ),
                  // ),
                ],
              ),
            ),
          ),
          SizedBox(height: 8),
          Divider(color: Color(0xFFD2E4F1), thickness: 1, indent: 12, endIndent: 12),
          Row(
            children: [
              SizedBox(width: 12),
              Container(
                width: 48,
                height: 48,
                alignment: Alignment.center,
                child: GestureDetector(
                  key: _reactionButtonKey,
                  onTap      : () => _handleSingleTap(story),   //  ☑ like / unlike
                  onLongPress: () => setState(() => showPopup = true),
                  child: SvgPicture.asset(
                    getReactionAsset(userReactionType ?? ReactionType.none),
                    width: 24,
                    height: 24,
                  ),
                ),
              ),
              SizedBox(width: 2),
              SvgPicture.asset(
                'assets/images/comment_stories.svg',
                width: 24,
                height: 24,
              ),
              SizedBox(width: 1),              
              Text(
                story.commentCount?.toString() ?? '0',
                style: GoogleFonts.openSans(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              SizedBox(width: 8),
              GestureDetector(
                onTap: () {
                  showReportBottomSheet(
                    context,
                    postId: story.id!,
                    imageKey: story.postAttachments?.isNotEmpty == true
                        ? story.postAttachments!.first.attachmentKey
                        : null, // NEW: pass image key if available
                    onSubmit: ({
                      required int postId,
                      required int reasonType,
                      required String remarks,
                      String? imageKey, // NEW: add imageKey parameter
                    }) async {
                      await provider.reportPost(
                        postId: postId,
                        reasonType: reasonType,  // value already 1-based
                        remarks: remarks,
                        imageKey: imageKey, // NEW: pass image key
                      );
                    },
                  );
                },
                child: SvgPicture.asset(
                  'assets/images/Flag_stories.svg',
                  width: 24,
                  height: 24,
                ),
              ),
              Spacer(),
              Icon(Icons.remove_red_eye, size: 18, color: Color(0xFF7B7B7B)),
              SizedBox(width: 4),
              Text(
                '${story.viewCount ?? 0} Views',
                style: GoogleFonts.openSans(
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: Color(0xFF7B7B7B),
                ),
              ),
              SizedBox(width: 12),
            ],
          ),
          SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(
              _formatDateTime(story.createdDate),
              style: GoogleFonts.openSans(
                fontSize: 10,
                color: Color(0xFF7B7B7B),
              ),
            ),
          ),
        ],
      ),
    );

    return Stack(
      children: [
        cardContent,
        if (showPopup)
          Positioned(
            bottom: 40,
            child: _ReactionPopup(
              onSelect: (int type) async {
                setState(() => showPopup = false);
                await provider.reactToPost(
                  postId: story.id!,
                  reactionType: type,
                  imageKey: story.postAttachments?.isNotEmpty == true
                      ? story.postAttachments!.first.attachmentKey
                      : null, // NEW: pass image key if available
                );
              },
            ),
          ),
      ],
    );
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) {
      return 'Unknown date';
    }
    final now = DateTime.now();
    final diff = now.difference(dateTime);
    if (diff.inMinutes < 60) {
      return '${diff.inMinutes} minutes ago';
    } else if (diff.inHours < 24) {
      return '${diff.inHours} hours ago';
    } else {
      return '${diff.inDays} days ago';
    }
  }

  List<Widget> _buildActiveReactions(ReactionCountsByGroup? group) {
    if (group == null) return [];
    final List<Widget> widgets = [];
    final reactions = [
      {'count': group.likeCount ?? 0, 'asset': 'assets/images/thumbsup_stories.svg'},
      {'count': group.heartCount ?? 0, 'asset': 'assets/images/heart_stories_red.svg'},
      {'count': group.clapsCount ?? 0, 'asset': 'assets/images/clap_stories.svg'},
      {'count': group.sadCount ?? 0, 'asset': 'assets/images/smile_stories.svg'},
    ];
    for (final reaction in reactions) {
      if (((reaction['count'] as int? ?? 0) > 0)) {
        widgets.add(Row(
          children: [
            SvgPicture.asset(
              reaction['asset'] as String,
              width: 18,
              height: 18,
            ),
            SizedBox(width: 2),
            Text(
              reaction['count'].toString(),
              style: GoogleFonts.openSans(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
            SizedBox(width: 8),
          ],
        ));
      }
    }
    return widgets;
  }
}

//---------------------------------------------------------------------------
// Small reusable popup widget
//---------------------------------------------------------------------------
class _ReactionPopup extends StatelessWidget {
  const _ReactionPopup({Key? key, required this.onSelect}) : super(key: key);

  final ValueChanged<int> onSelect;

  @override
  Widget build(BuildContext context) {
    final items = <Map<String, dynamic>>[
      {'t': ReactionType.thumbsup, 'a': 'assets/images/thumbsup_stories.svg'},
      {'t': ReactionType.heart,    'a': 'assets/images/heart_stories_red.svg'},
      {'t': ReactionType.clap,     'a': 'assets/images/clap_stories.svg'},
      {'t': ReactionType.smile,    'a': 'assets/images/smile_stories.svg'},
    ];
    return Material(
      color: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
                blurRadius: 6,
                color: Colors.black12,
                offset: Offset(0, 2)),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: items.map((e) {
            return GestureDetector(
              onTap: () => onSelect(e['t'] as int),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 6),
                child: SvgPicture.asset(e['a'] as String, width: 28),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
