import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/calendar/components/calendarTimeUtils.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/calendar/meetingDetails.dart';
import 'package:seawork/screens/employee/employee/providers/meetingProvider.dart';
import 'package:seawork/screens/employee/employee/models/meeting.dart';
import 'package:seawork/utils/util.dart';

class AgendaScreen extends ConsumerWidget {
  final DateTime selectedDate;

  const AgendaScreen({Key? key, required this.selectedDate}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final calendarState = ref.watch(graphCalendarProvider);
    final List<Meeting> allMeetings = calendarState.meetings;
    final Map<String, List<Meeting>> meetingsByDate = {};
    for (final meeting in allMeetings) {
      if (!meetingsByDate.containsKey(meeting.date)) {
        meetingsByDate[meeting.date] = [];
      }
      meetingsByDate[meeting.date]!.add(meeting);
    }
    final List<DateTime> datesRange = List.generate(
      28,
      (index) => selectedDate.add(Duration(days: index)),
    );

    return Container(
      color: AppColors.secondaryColor,
      // child: RefreshIndicator(
      //   onRefresh:
      //       () => ref.read(graphCalendarProvider.notifier).refreshEvents(),
      child: ListView.builder(
        physics: const BouncingScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        itemCount: datesRange.length,
        itemBuilder: (context, index) {
          final date = datesRange[index];
          return _buildDateSection(date, meetingsByDate, context);
        },
      ),
      // ),
    );
  }

  Widget _buildDateSection(
    DateTime date,
    Map<String, List<Meeting>> meetingsByDate,
    BuildContext context,
  ) {
    final dateString = _formatDateForComparison(date);
    final meetings = meetingsByDate[dateString] ?? [];
    meetings.sort((a, b) => a.time.compareTo(b.time));
    final formattedDate = DateFormat('MMM d').format(date);
    final dayLabel = _isToday(date) ? "Today" : DateFormat('EEEE').format(date);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Date header
        Padding(
          padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
          child: Row(
            children: [
              DmSansText(
                formattedDate,
                fontWeight: FontWeight.w500,
                fontSize: 16,
                color: AppColors.viewColor,
              ),
              const SizedBox(width: 8),
              OpenSansText(
                dayLabel,
                fontSize: 12,
                color: AppColors.darkGreyColor,
                fontWeight: FontWeight.w400,
              ),
            ],
          ),
        ),

        if (meetings.isEmpty)
          Padding(
            padding: EdgeInsets.only(bottom: 16.0),
            child: OpenSansText(
              'No events',
              fontWeight: FontWeight.w400,
              fontSize: 12,
              color: AppColors.darkGreyColor,
            ),
          )
        else
          _buildMeetingsTimeline(meetings, date, context),
      ],
    );
  }

  Widget _buildMeetingsTimeline(
    List<Meeting> meetings,
    DateTime meetingDate,
    BuildContext context,
  ) {
    // Get screen width for responsive sizing
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate a reasonable height per item based on content - more aggressive for iPhone SE
        final estimatedItemHeight =
            (screenWidth <= 375 && screenHeight <= 667)
                ? 90.0
                : (screenWidth <= 375)
                ? 100.0
                : 110.0;
        final totalHeight = meetings.length * estimatedItemHeight;

        return Stack(
          children: [
            // Continuous timeline line
            Padding(
              padding: EdgeInsets.only(left: 74.0, top: 6),
              child: Container(
                width: 2,
                height: meetings.isEmpty ? 60 : totalHeight + 6,
                color: AppColors.meetingdurationcolor,
              ),
            ),

            // Meetings column
            Column(
              children:
                  meetings.map((meeting) {
                    return Padding(
                      padding: EdgeInsets.only(bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                              top: 16.0,
                              left: 10,
                              right: 12,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                DmSansText(
                                  TimeUtils.formatTimeToDisplay(
                                    meeting.time,
                                    meetingDate,
                                  ),
                                  fontWeight: FontWeight.w400,
                                  fontSize: 12,
                                  color: AppColors.meetingdurationcolor,
                                ),
                                SizedBox(height: 8),
                                DmSansText(
                                  '${meeting.durationMinutes} min',
                                  color: AppColors.darkGreyColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 10.0,
                              ),
                              child: _buildMeetingCard(
                                meeting,
                                meetingDate,
                                context,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
            ),
          ],
        );
      },
    );
  }

  // Meeting Card Widget
  Widget _buildMeetingCard(
    Meeting meeting,
    DateTime meetingDate,
    BuildContext context,
  ) {
    String locationText = "Microsoft Teams";

    if (meeting.location != null && meeting.location!.isNotEmpty) {
      locationText = meeting.location!;
    } else if (meeting.isOnline && meeting.onlineMeetingProvider.isNotEmpty) {
      if (meeting.onlineMeetingProvider == 'teamsForBusiness') {
        locationText = "Microsoft Teams";
      } else {
        locationText = meeting.onlineMeetingProvider;
      }
    }
    final isEnded = TimeUtils.isMeetingEnded(meeting);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        splashColor: AppColors.microinteraction,
        highlightColor: AppColors.microinteraction,
        onTap: () async {
          await Future.delayed(const Duration(milliseconds: 150));
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => MeetingDetailsScreen(meeting: meeting),
            ),
          );
        },
        child: Ink(
          decoration: BoxDecoration(
            color: AppColors.calendermeetColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppColors.calendermeetborderColor,
              width: 0.5,
            ),
            boxShadow: const [
              BoxShadow(color: AppColors.boxshadow, offset: Offset(0, 0)),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DmSansText(
                meeting.title,
                color:
                    (meeting.isCancelled || isEnded)
                        ? AppColors.darkGreyColor
                        : AppColors.blackColor,
                fontWeight: FontWeight.w600,
                fontSize: 16,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textDecoration:
                    meeting.isCancelled
                        ? TextDecoration.lineThrough
                        : TextDecoration.none,
              ),
              SizedBox(height: 12),
              DmSansText(
                capitalizeFirstWordOnly(locationText),
                color:
                    meeting.isCancelled
                        ? AppColors.darkGreyColor
                        : AppColors.lightBlack,
                fontSize: 12,
                fontWeight: FontWeight.w500,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  String _formatDateForComparison(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }
}
