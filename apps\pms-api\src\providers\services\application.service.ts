import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { ApplicationModel } from '../models/application/application.model';
import { ApplicationPreferenceModel } from '../models/application/applicationPreference.model';
import { ApplicationReviewMVModel } from '../models/application/applicationReviewMV.model';
import { ApplicationStatusModel } from '../models/application/applicationStatus.model';
import { ApplicationViewModelModel } from '../models/application/applicationViewModel.model';
import { UpdatePendingApplicationsVMModel } from '../models/application/updatePendingApplicationsVM.model';
import { VerificationReAssignmentModel } from '../models/application/verificationReAssignment.model';
@Injectable()
export class ApplicationService {
  constructor(private readonly http: HttpService) {}

  public async addUpdateApplication(
    model: ApplicationModel,
  ): Promise<ApplicationModel> {
    // -- not used
    const response = await this.http.post<ApplicationModel>(
      `Application/AddUpdateApplication`,
      model,
    );
    return response;
  }

  public async getApplicationsByParentId(
    parentId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetApplicationsByParentId`,
      { parentId: parentId },
      true,
    );
    return response;
  }

  public async getApplicationsByReviewerId(
    pageNumber?: number,
    pageSize?: number,
    code?: string,
    reviewerId?: number,
    fullNameInEnglish?: string,
    applicationStatusId?: number,
    selAcademicYearId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetApplicationsByReviwerId`,
      {
        PageNumber: pageNumber,
        PageSize: pageSize,
        Code: code,
        ReviewerId: reviewerId,
        FullNameInEnglish: fullNameInEnglish,
        ApplicationStatusId: applicationStatusId,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async getApplicationsBySecondReviewerId(
    pageNumber?: number,
    pageSize?: number,
    code?: string,
    reviewerId?: number,
    fullNameInEnglish?: string,
    applicationStatusId?: number,
    selAcademicYearId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetApplicationsBySecondReviwerId`,
      {
        PageNumber: pageNumber,
        PageSize: pageSize,
        Code: code,
        ReviewerId: reviewerId,
        FullNameInEnglish: fullNameInEnglish,
        ApplicationStatusId: applicationStatusId,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async deleteApplication(
    model: ApplicationModel,
  ): Promise<ApplicationModel> {
    // -- not used
    const response = await this.http.post<ApplicationModel>(
      `Application/DeleteApplication`,
      model,
    );
    return response;
  }

  public async updateApplicationNurseryPreference(
    model: ApplicationPreferenceModel,
  ): Promise<ApplicationPreferenceModel> {
    // -- not used
    const response = await this.http.post<ApplicationPreferenceModel>(
      `Application/UpdateApplicationNurseryPreference`,
      model,
    );
    return response;
  }

  public async getNurserySuggestionsByKidInfoId(
    kidInfoId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetNurserySuggesionsByKidInfoId`,
      { kidInfoId: kidInfoId },
      true,
    );
    return response;
  }

  public async cancelApplication(
    applicationId?: number,
    isDeleteReviewHistory?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get<any>(
      `Application/CancelApplication`,
      {
        applicationId: applicationId,
        IsDeleteReviewHistory: isDeleteReviewHistory,
      },
      false,
    );
    return response;
  }

  public async getApplicationForReviewById(
    applicationId?: number,
  ): Promise<ApplicationReviewMVModel> {
    // -- not used
    const response = await this.http.get<ApplicationReviewMVModel>(
      `Application/GetApplicationForReviewById`,
      { applicationId: applicationId },
      false,
    );
    return response;
  }

  public async updateApplicationStatus(
    model: ApplicationStatusModel,
  ): Promise<ApplicationStatusModel> {
    // -- not used
    const response = await this.http.post<ApplicationStatusModel>(
      `Application/UpdateApplicationStatus`,
      model,
    );
    return response;
  }

  public async getRevertedSectionsByParentId(
    parentId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetRevertedSectionsByParentId`,
      { parentId: parentId },
      true,
    );
    return response;
  }

  public async getAllParentEditableSections(
    parentId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetAllParentEditableSections`,
      { parentId: parentId },
      true,
    );
    return response;
  }

  public async updateApplicationReview(
    model: ApplicationReviewMVModel,
  ): Promise<ApplicationReviewMVModel> {
    // -- not used
    const response = await this.http.post<ApplicationReviewMVModel>(
      `Application/UpdateApplicationReview`,
      model,
    );
    return response;
  }

  public async getAllottedApplicationsForAdmin(
    pageNumber?: number,
    pageSize?: number,
    allotmentCreatedDateFrom?: string,
    allotmentCreatedDateTo?: string,
    appliedDateFrom?: string,
    appliedDateTo?: string,
    enrolledDateFrom?: string,
    enrolledDateTo?: string,
    lastUpdatedFrom?: string,
    lastUpdatedTo?: string,
    code?: string,
    reviewer?: string,
    fullNameInEnglish?: string,
    fatherName?: string,
    motherName?: string,
    phoneNumber?: string,
    nurseryId?: number,
    nurseryPrefId1?: number,
    nurseryPrefId2?: number,
    nurseryPrefId3?: number,
    childCode?: string,
    applicationStatusId?: number,
    admissionStatusId?: number,
    selAcademicYearId?: number,
    allottedNurseryId?: number,
    verifier?: string,
    displayStatus?: string,
    reasonForCancellation?: string,
    hasSiblings?: string,
    fatherNationality?: string,
    fatherEmployerTypeId?: number,
    fatherEmployerName?: string,
    fatherIssuingAuthority?: string,
    fatherIsWorking?: boolean,
    fatherDisabilityStatus?: boolean,
    motherNationality?: string,
    motherEmployerTypeId?: number,
    motherEmployerName?: string,
    motherIssuingAuthority?: string,
    motherIsWorking?: boolean,
    motherDisabilityStatus?: boolean,
    gradeId?: number,
    reviewerName?: string,
    verifierName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetAllottedApplicationsForAdmin`,
      {
        PageNumber: pageNumber,
        PageSize: pageSize,
        AllotmentCreatedDateFrom: allotmentCreatedDateFrom,
        AllotmentCreatedDateTo: allotmentCreatedDateTo,
        AppliedDateFrom: appliedDateFrom,
        AppliedDateTo: appliedDateTo,
        EnrolledDateFrom: enrolledDateFrom,
        EnrolledDateTo: enrolledDateTo,
        LastUpdatedFrom: lastUpdatedFrom,
        LastUpdatedTo: lastUpdatedTo,
        Code: code,
        Reviewer: reviewer,
        FullNameInEnglish: fullNameInEnglish,
        FatherName: fatherName,
        MotherName: motherName,
        PhoneNumber: phoneNumber,
        NurseryId: nurseryId,
        NurseryPrefId1: nurseryPrefId1,
        NurseryPrefId2: nurseryPrefId2,
        NurseryPrefId3: nurseryPrefId3,
        ChildCode: childCode,
        ApplicationStatusId: applicationStatusId,
        AdmissionStatusId: admissionStatusId,
        SelAcademicYearId: selAcademicYearId,
        AllottedNurseryId: allottedNurseryId,
        Verifier: verifier,
        DisplayStatus: displayStatus,
        ReasonForCancellation: reasonForCancellation,
        HasSiblings: hasSiblings,
        FatherNationality: fatherNationality,
        FatherEmployerTypeId: fatherEmployerTypeId,
        FatherEmployerName: fatherEmployerName,
        FatherIssuingAuthority: fatherIssuingAuthority,
        FatherIsWorking: fatherIsWorking,
        FatherDisabilityStatus: fatherDisabilityStatus,
        MotherNationality: motherNationality,
        MotherEmployerTypeId: motherEmployerTypeId,
        MotherEmployerName: motherEmployerName,
        MotherIssuingAuthority: motherIssuingAuthority,
        MotherIsWorking: motherIsWorking,
        MotherDisabilityStatus: motherDisabilityStatus,
        GradeId: gradeId,
        ReviewerName: reviewerName,
        VerifierName: verifierName,
      },
      true,
    );
    return response;
  }

  public async getAllottedApplicationsForAdminNotOptimized(
    pageNumber?: number,
    pageSize?: number,
    allotmentCreatedDateFrom?: string,
    allotmentCreatedDateTo?: string,
    appliedDateFrom?: string,
    appliedDateTo?: string,
    enrolledDateFrom?: string,
    enrolledDateTo?: string,
    lastUpdatedFrom?: string,
    lastUpdatedTo?: string,
    code?: string,
    reviewer?: string,
    fullNameInEnglish?: string,
    fatherName?: string,
    motherName?: string,
    phoneNumber?: string,
    nurseryId?: number,
    nurseryPrefId1?: number,
    nurseryPrefId2?: number,
    nurseryPrefId3?: number,
    childCode?: string,
    applicationStatusId?: number,
    admissionStatusId?: number,
    selAcademicYearId?: number,
    allottedNurseryId?: number,
    verifier?: string,
    displayStatus?: string,
    reasonForCancellation?: string,
    hasSiblings?: string,
    fatherNationality?: string,
    fatherEmployerTypeId?: number,
    fatherEmployerName?: string,
    fatherIssuingAuthority?: string,
    fatherIsWorking?: boolean,
    fatherDisabilityStatus?: boolean,
    motherNationality?: string,
    motherEmployerTypeId?: number,
    motherEmployerName?: string,
    motherIssuingAuthority?: string,
    motherIsWorking?: boolean,
    motherDisabilityStatus?: boolean,
    gradeId?: number,
    reviewerName?: string,
    verifierName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetAllottedApplicationsForAdminNotOptimed`,
      {
        PageNumber: pageNumber,
        PageSize: pageSize,
        AllotmentCreatedDateFrom: allotmentCreatedDateFrom,
        AllotmentCreatedDateTo: allotmentCreatedDateTo,
        AppliedDateFrom: appliedDateFrom,
        AppliedDateTo: appliedDateTo,
        EnrolledDateFrom: enrolledDateFrom,
        EnrolledDateTo: enrolledDateTo,
        LastUpdatedFrom: lastUpdatedFrom,
        LastUpdatedTo: lastUpdatedTo,
        Code: code,
        Reviewer: reviewer,
        FullNameInEnglish: fullNameInEnglish,
        FatherName: fatherName,
        MotherName: motherName,
        PhoneNumber: phoneNumber,
        NurseryId: nurseryId,
        NurseryPrefId1: nurseryPrefId1,
        NurseryPrefId2: nurseryPrefId2,
        NurseryPrefId3: nurseryPrefId3,
        ChildCode: childCode,
        ApplicationStatusId: applicationStatusId,
        AdmissionStatusId: admissionStatusId,
        SelAcademicYearId: selAcademicYearId,
        AllottedNurseryId: allottedNurseryId,
        Verifier: verifier,
        DisplayStatus: displayStatus,
        ReasonForCancellation: reasonForCancellation,
        HasSiblings: hasSiblings,
        FatherNationality: fatherNationality,
        FatherEmployerTypeId: fatherEmployerTypeId,
        FatherEmployerName: fatherEmployerName,
        FatherIssuingAuthority: fatherIssuingAuthority,
        FatherIsWorking: fatherIsWorking,
        FatherDisabilityStatus: fatherDisabilityStatus,
        MotherNationality: motherNationality,
        MotherEmployerTypeId: motherEmployerTypeId,
        MotherEmployerName: motherEmployerName,
        MotherIssuingAuthority: motherIssuingAuthority,
        MotherIsWorking: motherIsWorking,
        MotherDisabilityStatus: motherDisabilityStatus,
        GradeId: gradeId,
        ReviewerName: reviewerName,
        VerifierName: verifierName,
      },
      true,
    );
    return response;
  }

  public async getApplicationsForAdmin(
    pageNumber?: number,
    pageSize?: number,
    appliedDateFrom?: string,
    appliedDateTo?: string,
    lastUpdatedFrom?: string,
    lastUpdatedTo?: string,
    code?: string,
    reviewer?: string,
    fullNameInEnglish?: string,
    fatherName?: string,
    motherName?: string,
    phoneNumber?: string,
    nurseryId?: number,
    nurseryPrefId1?: number,
    nurseryPrefId2?: number,
    nurseryPrefId3?: number,
    childCode?: string,
    applicationStatusId?: number,
    admissionStatusId?: number,
    selAcademicYearId?: number,
    allottedNurseryId?: number,
    verifier?: string,
    reasonForCancellation?: string,
    hasSiblings?: string,
    fatherNationality?: string,
    fatherEmployerTypeId?: number,
    fatherIssuingAuthority?: string,
    fatherIsWorking?: boolean,
    fatherDisabilityStatus?: boolean,
    motherNationality?: string,
    motherEmployerTypeId?: number,
    motherIssuingAuthority?: string,
    motherIsWorking?: boolean,
    motherDisabilityStatus?: boolean,
    gradeId?: number,
    reviewerName?: string,
    verifierName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetApplicationsForAdmin`,
      {
        PageNumber: pageNumber,
        PageSize: pageSize,
        AppliedDateFrom: appliedDateFrom,
        AppliedDateTo: appliedDateTo,
        LastUpdatedFrom: lastUpdatedFrom,
        LastUpdatedTo: lastUpdatedTo,
        Code: code,
        Reviewer: reviewer,
        FullNameInEnglish: fullNameInEnglish,
        FatherName: fatherName,
        MotherName: motherName,
        PhoneNumber: phoneNumber,
        NurseryId: nurseryId,
        NurseryPrefId1: nurseryPrefId1,
        NurseryPrefId2: nurseryPrefId2,
        NurseryPrefId3: nurseryPrefId3,
        ChildCode: childCode,
        ApplicationStatusId: applicationStatusId,
        AdmissionStatusId: admissionStatusId,
        SelAcademicYearId: selAcademicYearId,
        AllottedNurseryId: allottedNurseryId,
        Verifier: verifier,
        ReasonForCancellation: reasonForCancellation,
        HasSiblings: hasSiblings,
        FatherNationality: fatherNationality,
        FatherEmployerTypeId: fatherEmployerTypeId,
        FatherIssuingAuthority: fatherIssuingAuthority,
        FatherIsWorking: fatherIsWorking,
        FatherDisabilityStatus: fatherDisabilityStatus,
        MotherNationality: motherNationality,
        MotherEmployerTypeId: motherEmployerTypeId,
        MotherIssuingAuthority: motherIssuingAuthority,
        MotherIsWorking: motherIsWorking,
        MotherDisabilityStatus: motherDisabilityStatus,
        GradeId: gradeId,
        ReviewerName: reviewerName,
        VerifierName: verifierName,
      },
      true,
    );
    return response;
  }

  public async getApplicationsForAdminNotOptimized(
    pageNumber?: number,
    pageSize?: number,
    lastUpdatedFrom?: string,
    lastUpdatedTo?: string,
    code?: string,
    reviewer?: string,
    fullNameInEnglish?: string,
    fatherName?: string,
    motherName?: string,
    phoneNumber?: string,
    nurseryId?: number,
    nurseryPrefId1?: number,
    nurseryPrefId2?: number,
    nurseryPrefId3?: number,
    childCode?: string,
    applicationStatusId?: number,
    admissionStatusId?: number,
    selAcademicYearId?: number,
    allottedNurseryId?: number,
    verifier?: string,
    reasonForCancellation?: string,
    hasSiblings?: string,
    fatherNationality?: string,
    fatherEmployerTypeId?: number,
    fatherIssuingAuthority?: string,
    fatherIsWorking?: boolean,
    fatherDisabilityStatus?: boolean,
    motherNationality?: string,
    motherEmployerTypeId?: number,
    motherIssuingAuthority?: string,
    motherIsWorking?: boolean,
    motherDisabilityStatus?: boolean,
    gradeId?: number,
    reviewerName?: string,
    verifierName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetApplicationsForAdminNotOptimized`,
      {
        PageNumber: pageNumber,
        PageSize: pageSize,
        LastUpdatedFrom: lastUpdatedFrom,
        LastUpdatedTo: lastUpdatedTo,
        Code: code,
        Reviewer: reviewer,
        FullNameInEnglish: fullNameInEnglish,
        FatherName: fatherName,
        MotherName: motherName,
        PhoneNumber: phoneNumber,
        NurseryId: nurseryId,
        NurseryPrefId1: nurseryPrefId1,
        NurseryPrefId2: nurseryPrefId2,
        NurseryPrefId3: nurseryPrefId3,
        ChildCode: childCode,
        ApplicationStatusId: applicationStatusId,
        AdmissionStatusId: admissionStatusId,
        SelAcademicYearId: selAcademicYearId,
        AllottedNurseryId: allottedNurseryId,
        Verifier: verifier,
        ReasonForCancellation: reasonForCancellation,
        HasSiblings: hasSiblings,
        FatherNationality: fatherNationality,
        FatherEmployerTypeId: fatherEmployerTypeId,
        FatherIssuingAuthority: fatherIssuingAuthority,
        FatherIsWorking: fatherIsWorking,
        FatherDisabilityStatus: fatherDisabilityStatus,
        MotherNationality: motherNationality,
        MotherEmployerTypeId: motherEmployerTypeId,
        MotherIssuingAuthority: motherIssuingAuthority,
        MotherIsWorking: motherIsWorking,
        MotherDisabilityStatus: motherDisabilityStatus,
        GradeId: gradeId,
        ReviewerName: reviewerName,
        VerifierName: verifierName,
      },
      true,
    );
    return response;
  }

  public async getAuditHistory(
    applicationId?: number,
    key?: string,
    isKid?: boolean,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetAuditHistory`,
      { applicationId: applicationId, key: key, isKid: isKid },
      true,
    );
    return response;
  }

  public async updateNationalityReviewStatus(): Promise<ApplicationModel> {
    // -- not used
    const response = await this.http.get<ApplicationModel>(
      `Application/UpdateNationalityReviewStatus`,
      {},
      false,
    );
    return response;
  }

  public async applicationTransferToSpecificNurseryCountByClass(
    nurseryIds?: string,
    name?: string,
    gradeDisplayName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/ApplicationTransfertoSpecificNurseryCountbyClass`,
      {
        NurseryIds: nurseryIds,
        Name: name,
        GradeDisplayName: gradeDisplayName,
      },
      true,
    );
    return response;
  }

  public async verificationReAssignment(
    model: VerificationReAssignmentModel,
  ): Promise<VerificationReAssignmentModel> {
    // -- not used
    const response = await this.http.post<VerificationReAssignmentModel>(
      `Application/VerificationReAssignment`,
      model,
    );
    return response;
  }

  public async getPreviousAcademicYearPendingApplications(
    parentId?: number,
  ): Promise<UpdatePendingApplicationsVMModel[]> {
    // -- not used
    const response = await this.http.get<UpdatePendingApplicationsVMModel[]>(
      `Application/GetPreviousAcademicYearPendingApplications`,
      { ParentId: parentId },
      true,
    );
    return response;
  }

  public async updateApplicationForNewAcademicYear(
    model: UpdatePendingApplicationsVMModel,
  ): Promise<UpdatePendingApplicationsVMModel> {
    // -- not used
    const response = await this.http.post<UpdatePendingApplicationsVMModel>(
      `Application/UpdateApplicationForNewAcademicYear`,
      model,
    );
    return response;
  }

  public async previewProfileDetails(
    kidInfoId?: number,
  ): Promise<ApplicationViewModelModel> {
    // -- not used
    const response = await this.http.get<ApplicationViewModelModel>(
      `Application/PreviewProfileDetails`,
      { KidInfoId: kidInfoId },
      false,
    );
    return response;
  }

  public async getRevertAutoCancelInfo(
    applicationId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetRevertAutoCancelInfo`,
      { ApplicationId: applicationId },
      true,
    );
    return response;
  }

  public async adminConsentRequest(
    applicationId?: number,
  ): Promise<ApplicationModel> {
    // -- not used
    const response = await this.http.get<ApplicationModel>(
      `Application/AdminConsentRequest`,
      { ApplicationId: applicationId },
      false,
    );
    return response;
  }

  public async getConsentRequestedApplicationsByParentId(
    parentId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.http.get<ApplicationModel[]>(
      `Application/GetConsentRequestedApplicationsByParentId`,
      { parentId: parentId },
      true,
    );
    return response;
  }

  public async updateMultipleApplicationConsentRequest(
    model: ApplicationModel,
  ): Promise<ApplicationModel> {
    // -- not used
    const response = await this.http.post<ApplicationModel>(
      `Application/UpdateMultipleApplicationConsentRequest`,
      model,
    );
    return response;
  }
}
