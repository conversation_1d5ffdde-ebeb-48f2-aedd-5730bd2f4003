import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/form/searchInput.dart';
import 'package:seawork/components/spacing/padding.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/utils/style/colors.dart';

final searchProvider = StateProvider<String>((ref) => '');

Future<void> ShowRoundedModalBottomSheetForListSelection(
  BuildContext context,
  String selectedFilter,
  Function(String) onFilterSelected,
  List<String> filterOptions,
) {
  return ShowRoundedModalBottomSheet(context, (BuildContext context) {
    return BottomSheetListSelector(
      currentFilter: selectedFilter,
      onFilterSelected: onFilterSelected,
      filterOptions: filterOptions,
    );
  });
}

Future ShowRoundedModalBottomSheet(
  BuildContext context,
  WidgetBuilder builder,
) {
  return showModalBottomSheet(
    context: context,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    builder: builder,
  );
}

class BottomSheetListSelector extends StatelessWidget {
  final String currentFilter;
  final Function(String) onFilterSelected;
  final List<String> filterOptions;

  BottomSheetListSelector({
    Key? key,
    required this.currentFilter,
    required this.onFilterSelected,
    required this.filterOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sheet indicator line
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 30,
            height: 5,
            decoration: BoxDecoration(
              color: AppColors.viewColor,
              borderRadius: BorderRadius.circular(9),
            ),
          ),
          const SizedBox(height: 20),

          // Filter options
          for (String filter in filterOptions)
            _buildFilterOption(context, filter),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildFilterOption(BuildContext context, String filterName) {
    final isSelected = currentFilter == filterName;

    return InkWell(
      onTap: () {
        onFilterSelected(filterName);
        Navigator.pop(context);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.lightGreyColor2 : Colors.white,
          borderRadius: BorderRadius.circular(4),
        ),
        child: OpenSansText(
          filterName,
          textAlign: TextAlign.center,
          fontSize: 14,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          color: const Color(0xFF404040),
        ),
      ),
    );
  }
}

class BottomSheetDropdown extends StatefulWidget {
  final String selectedFilter;
  final WidgetRef ref;
  final dynamic Function(String) onFilterSelected;
  final List<String> filterOptions;

  const BottomSheetDropdown({
    super.key,
    required this.selectedFilter,
    required this.ref,
    required this.onFilterSelected,
    required this.filterOptions,
  });

  @override
  State<BottomSheetDropdown> createState() => _BottomSheetDropdownState();
}

class _BottomSheetDropdownState extends State<BottomSheetDropdown> {
  bool isSheetOpen = false;

  Future<void> _handleTap() async {
    setState(() => isSheetOpen = true);

    await ShowRoundedModalBottomSheetForListSelection(
      context,
      widget.selectedFilter,
      widget.onFilterSelected,
      widget.filterOptions,
    );

    setState(() => isSheetOpen = false);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: Padding12x6(
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.lightGreyColor2, width: 1.0),
            borderRadius: BorderRadius.circular(8),
            color: AppColors.whiteColor,
          ),
          height: 43,
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              DMSans600Medium(
                14,
                widget.selectedFilter,
                AppColors.blackColor,
                fontWeight: FontWeight.w400,
              ),
              Transform.rotate(
                angle: isSheetOpen ? 3.14159 : 0,
                child: CustomSvgImage(imageName: "ArrowLeft"),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CommonBottomSheet extends StatelessWidget {
  final Widget content;
  final double? height;

  const CommonBottomSheet({Key? key, required this.content, this.height})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    // If height is not provided, calculate it dynamically
    final appBarHeight = kToolbarHeight;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final sheetHeight =
        height ??
        (MediaQuery.of(context).size.height - (appBarHeight + statusBarHeight));

    return Container(
      height: sheetHeight,
      decoration: const BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
        child: content,
      ),
    );
  }
}

class CommonBottomSheetWithSearch extends ConsumerWidget {
  final List<String> items;
  final void Function(String) onItemTap;
  final String? selectedItem;
  final String? headingText;
  final String? bottomOptionText;
  final ScrollController? scrollController;
  const CommonBottomSheetWithSearch({
    Key? key,
    required this.items,
    required this.onItemTap,
    this.selectedItem,
    this.headingText,
    this.bottomOptionText,
    this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final query = ref.watch(searchProvider);
    final filteredItems = items
        .where((item) => item.toLowerCase().contains(query.toLowerCase()))
        .toList();

    return Material(
      color: AppColors.whiteColor,
      child: SafeArea(
        child: ListView(
          controller: scrollController,
          padding: EdgeInsets.zero,
          children: [
            const SizedBox(height: 12),
            const Center(
              child: CustomSvgImage(imageName: 'ic_draghandler'),
              // child: SizedBox(
              //   width: 30,
              //   height: 5,
              //   child: DecoratedBox(
              //     decoration: BoxDecoration(
              //       color: AppColors.viewColor,
              //       borderRadius: BorderRadius.all(Radius.circular(9)),
              //     ),
              //   ),
              // ),
            ),
            if (headingText != null) ...[
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: DMSans600Medium(
                    16,
                    headingText!,
                    AppColors.blackColor,
                  ),
                ),
              ),
            ],
            SearchInput(
              context,
              topMargin: 10,
              hintText: 'Search',
              defaultValue: query,
              onChange: (val) => ref.read(searchProvider.notifier).state = val,
              suffixIcon: const CustomSvgImage(imageName: "search_icon"),
            ),
            const SizedBox(height: 8),
            if (filteredItems.isEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomSvgImage(imageName: 'ic_nosearchresult'),
                    const SizedBox(height: 16),
                    OpenSansText(
                      'Try a different name or spelling',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppColors.blackColor,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            else
              ...filteredItems.map((item) {
                final isSelected = item == selectedItem;
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: InkWell(
                    onTap: () async {
                      // Wait briefly to show ripple before closing or acting
                      await Future.delayed(const Duration(milliseconds: 200));
                      onItemTap(item);
                    },
                    borderRadius: BorderRadius.circular(8),
                    splashColor:
                        AppColors.microinteraction, // Microinteraction color
                    highlightColor: AppColors.microinteraction, // On tap hold
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      decoration: BoxDecoration(
                        color:
                            isSelected
                                ? AppColors.lightGreyColor2
                                : AppColors.transparentColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(width: 12),
                          Expanded(
                            child: Center(
                              child: OpenSansText(
                                item,
                                color: AppColors.blackColor,
                                fontSize: 14,
                                fontWeight:
                                    isSelected
                                        ? FontWeight.w600
                                        : FontWeight.w400,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }
}

Future<T?> showCustomDraggableModalBottomSheet<T>({
  required BuildContext context,
  required Widget child,
  double initialChildSize = 0.93,
  double minChildSize = 0.5,
  double topPadding = 122.0,
  Color? backgroundColor = Colors.transparent,
  Color? draggableSheetColor = Colors.white,
}) {
  final screenHeight = MediaQuery.of(context).size.height;

  final maxHeight = screenHeight - topPadding;
  final maxChildSize = maxHeight / screenHeight;

  final adjustedInitialChildSize = initialChildSize.clamp(
    minChildSize,
    maxChildSize,
  );

  return showModalBottomSheet<T>(
    context: context,
    isScrollControlled: true,
    backgroundColor: backgroundColor,
    builder: (context) {
      return DraggableScrollableSheet(
        initialChildSize: adjustedInitialChildSize,
        minChildSize: minChildSize,
        maxChildSize: maxChildSize,
        builder:
            (_, scrollController) => ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20.0),
                topRight: Radius.circular(20.0),
              ),
              child: Container(color: draggableSheetColor, child: child),
            ),
      );
    },
  );
}

class CommonBottomSheetWithList extends StatefulWidget {
  final List<String> items;
  final void Function(String) onItemTap;
  final String? selectedItem;
  final String topText;

  const CommonBottomSheetWithList({
    Key? key,
    required this.items,
    required this.onItemTap,
    this.selectedItem,
    required this.topText,
  }) : super(key: key);

  @override
  _CommonBottomSheetWithListState createState() =>
      _CommonBottomSheetWithListState();
}

class _CommonBottomSheetWithListState extends State<CommonBottomSheetWithList> {
  String? _selectedItem;

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.selectedItem;
  }

  @override
  Widget build(BuildContext context) {
    return CommonBottomSheet(
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          const Center(
            child: SizedBox(
              width: 30,
              height: 5,
              child: DecoratedBox(
                decoration: BoxDecoration(
                  color: AppColors.viewColor,
                  borderRadius: BorderRadius.all(Radius.circular(9)),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.only(left: 16.0),
            child: OpenSans600Large(16, widget.topText, AppColors.blackColor),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: widget.items.length,
              itemBuilder: (context, index) {
                final item = widget.items[index];

                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedItem = item;
                      });
                      widget.onItemTap(item);
                    },
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      decoration: BoxDecoration(
                        color:
                            _selectedItem == item
                                ? AppColors.lightGreyColor2
                                : AppColors.transparentColor,
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Center(
                        child: OpenSansText(
                          item,
                          fontSize: 14,
                          fontWeight:
                              _selectedItem == item
                                  ? FontWeight.w600
                                  : FontWeight.w400,
                          color: AppColors.blackColor,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
