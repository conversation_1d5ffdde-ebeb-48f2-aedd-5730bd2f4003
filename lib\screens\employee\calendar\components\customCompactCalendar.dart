

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/absence/providers/absenceListNotifier.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/screens/employee/approval/providers/approvalsProvider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_types_provider.dart';

double fitSize(final double width,
    {double maxWidth = 0, final double minWidth = 0}) {
  var widthCalculated = width;
  if (maxWidth == 0) {
    maxWidth = width;
  }
  if (width > maxWidth) {
    widthCalculated = maxWidth;
  } else if (width < minWidth) {
    widthCalculated = minWidth;
  } else {
    widthCalculated = width;
  }
  return widthCalculated;
}

// Provider to manage the selected date range state
final dateRangeProvider = StateProvider<Map<String, DateTime?>?>((ref) => null);

class FilterBasedOnDates extends ConsumerStatefulWidget {
  final Map<String, dynamic>? selectedLeaveType;
  final bool isApproval;
  final bool isLetterRequest;
  final String? status;
  
  FilterBasedOnDates({
    this.selectedLeaveType,
    this.isApproval = false,
    this.isLetterRequest = false,
    this.status,
  });

  @override
  _CompactCalendarButtonState createState() => _CompactCalendarButtonState();
}

class _CompactCalendarButtonState extends ConsumerState<FilterBasedOnDates> {
  DateTime? _startDate;
  DateTime? _endDate;
  String? _startDateString;
  String? _endDateString;
  DateTime _focusedMonth = DateTime.now();
  bool _isSelectingStartDate = true;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _showCalendarDialog(context),
      child: Container(
        padding: const EdgeInsets.only(left: 12.0, right: 2.0, top: 8.0, bottom: 12.0),
        child: CustomSvgImage(imageName: "calendar"),
      ),
    );
  }

  void _showCalendarDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              insetPadding: EdgeInsets.all(16),
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.whiteColor,
                  borderRadius: BorderRadius.circular(16),
                ),
                width: MediaQuery.of(context).size.width * 0.9,
                height: MediaQuery.of(context).size.height * 0.5,
                padding: EdgeInsets.all(20),
                child: Column(
                  children: [ 
                    // Month navigation
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _focusedMonth = DateTime(_focusedMonth.year, _focusedMonth.month - 1);
                            });
                          },
                          icon: Icon(Icons.chevron_left, color: AppColors.chevroncolor),
                        ),
                        DmSansText(
                          DateFormat('MMMM yyyy').format(_focusedMonth),
                          color: AppColors.blackColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _focusedMonth = DateTime(_focusedMonth.year, _focusedMonth.month + 1);
                            });
                          },
                          icon: Icon(Icons.chevron_right, color: AppColors.chevroncolor),
                        ),
                      ],
                    ),

                    // Divider line
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 18),
                      child: Divider(
                        color: AppColors.dividercolor,
                        thickness: 1,
                        height: 20,
                      ),
                    ),
                    SizedBox(height: 16),
                    
                    // Calendar Grid
                    Expanded(
                      child: Column(
                        children: [
                          // Weekday headers
                          Row(
                            children: ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT']
                                .map((day) => Expanded(
                                      child: Center(
                                        child: OpenSansText(
                                          day,
                                          color: AppColors.lightBlack,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ))
                                .toList(),
                          ),
                          SizedBox(height: 8),

                          // Calendar days
                          Expanded(
                            child: _buildCalendarGrid(setState),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16),
                    
                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.viewColor,
                            padding: EdgeInsets.symmetric(horizontal: 42, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          onPressed: () {
                            // Update the dateRangeProvider with selected dates
                            if (_startDate != null && _endDate != null) {
                              ref.read(dateRangeProvider.notifier).state = {
                                'startDate': _startDate,
                                'endDate': _endDate,
                              };
                            }

                                    if (widget.isApproval) {
                                      ref.read(pendingTasksNotifierProvider.notifier).filterByDate(_startDate, _endDate);
                                      ref.read(approvedTasksNotifierProvider.notifier).filterByDate(_startDate, _endDate);
                                      ref.read(rejectedTasksNotifierProvider.notifier).filterByDate(_startDate, _endDate);
                                      ref.read(withdrawnTasksNotifierProvider.notifier).filterByDate(_startDate, _endDate);
                                    } else if (widget.isLetterRequest) {
                                      if (widget.status != null) {
                                        // final notifier = widget.status == "pending"
                                        //     ? ref.read(letterRequestPendingProvider.notifier)
                                        //     : widget.status == "approved"
                                        //         ? ref.read(letterRequestApprovedProvider.notifier)
                                        //         : widget.status == "rejected"
                                        //             ? ref.read(letterRequestRejectedProvider.notifier)
                                        //             : ref.read(letterRequestWithdrawProvider.notifier);
                                        final notifier = widget.status == "pending"
                                            ? ref.read(letterRequestPendingProvider.notifier)
                                            : ref.read(letterRequestApprovedProvider.notifier);

                                        notifier.setDateFilter(_startDate, _endDate);
                                      }
                                    } else {
                                      ref.read(awaitingListNotifierProvider.notifier).filterByDateRange(_startDate, _endDate);
                                      ref.read(approvedListNotifierProvider.notifier).filterByDateRange(_startDate, _endDate);
                                      ref.read(deniedListNotifierProvider.notifier).filterByDateRange(_startDate, _endDate);
                                      ref.read(withdrawnListNotifierProvider.notifier).filterByDateRange(_startDate, _endDate);
                                      ref.read(draftListNotifierProvider.notifier).filterByDateRange(_startDate, _endDate);
                                    }
                                  
                                  Navigator.pop(context);
                                },
                          child: DmSansText(
                            "Done",
                            color: AppColors.whiteColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCalendarGrid(StateSetter setState) {
    final firstDayOfMonth = DateTime(_focusedMonth.year, _focusedMonth.month, 1);
    final lastDayOfMonth = DateTime(_focusedMonth.year, _focusedMonth.month + 1, 0);
    final startingWeekday = firstDayOfMonth.weekday % 7; // 0 = Sunday
    final daysInMonth = lastDayOfMonth.day;

    List<Widget> dayWidgets = [];

    // Add empty spaces for days before the first day of the month
    for (int i = 0; i < startingWeekday; i++) {
      dayWidgets.add(Container());
    }

    // Add days of the month
    for (int day = 1; day <= daysInMonth; day++) {
      final currentDate = DateTime(_focusedMonth.year, _focusedMonth.month, day);
      final isSelected = _isDateSelected(currentDate);
      final isInRange = _isDateInRange(currentDate);

      dayWidgets.add(
        GestureDetector(
          onTap: () => _onDateTap(currentDate, setState),
          child: Container(
            margin: EdgeInsets.all(5),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: (isSelected || isInRange)
                  ? Border.all(color: AppColors.viewColor, width: 2)
                  : null,
            ),
            child: Center(
              child: OpenSansText(
                day.toString(),
                color: (isSelected || isInRange)
                    ? AppColors.viewColor
                    : AppColors.blackColor,
                fontSize: 14,
                fontWeight: (isSelected || isInRange) ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ),
        ),
      );
    }

    return GridView.count(
      crossAxisCount: 7,
      children: dayWidgets,
    );
  }

  bool _isDateSelected(DateTime date) {
    return (_startDate != null && _isSameDay(date, _startDate!)) ||
           (_endDate != null && _isSameDay(date, _endDate!));
  }

  bool _isDateInRange(DateTime date) {
    if (_startDate != null && _endDate != null) {
      return date.isAfter(_startDate!) && date.isBefore(_endDate!);
    }
    return false;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  void _onDateTap(DateTime date, StateSetter setState) {
    setState(() {
      if (_isSelectingStartDate || _startDate == null) {
        _startDate = date;
        _startDateString = DateFormat('dd-MM-yyyy').format(_startDate!);
        _isSelectingStartDate = false;
        _endDate = null; // Reset end date when selecting new start date
        _endDateString = null;
      } else {
        if (date.isBefore(_startDate!)) {
          // If selected date is before start date, make it the new start date
          _startDate = date;
          _startDateString = DateFormat('dd-MM-yyyy').format(_startDate!);
          _endDate = null;
          _endDateString = null;
        } else {
          // Set as end date
          _endDate = date;
          _endDateString = DateFormat('dd-MM-yyyy').format(_endDate!);
          _isSelectingStartDate = true; // Reset for next selection
        }
      }
    });
  }
}