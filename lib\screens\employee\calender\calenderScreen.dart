import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/calender/calendarAgendaScreen.dart';
import 'package:seawork/screens/employee/calender/calendarDayScreen.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/employee/providers/meetingProvider.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

class CalendarScreen extends ConsumerStatefulWidget {
  const CalendarScreen({Key? key}) : super(key: key);

  @override
  _CalendarScreenState createState() => _CalendarScreenState();
}

class _CalendarScreenState extends ConsumerState<CalendarScreen> {
  final int _selectedIndex = 1;
  String selectedView = 'Agenda';
  double _dragOffset = 0;

  @override
  void initState() {
    super.initState();
    // Initialize timezone database
    tz.initializeTimeZones();

    // Fetch events when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Get current date
      final now = DateTime.now();

      // Start date is 1 week ago, end date is 4 weeks from now
      final startDate = now.subtract(const Duration(days: 7));
      final endDate = now.add(const Duration(days: 28));

      // Fetch events for this range
      ref
          .read(graphCalendarProvider.notifier)
          .fetchEventsForDateRange(startDate, endDate);
    });
  }

  @override
  Widget build(BuildContext context) {
    final calendarState = ref.watch(graphCalendarProvider);
    final selectedDate = calendarState.selectedDate;
    final showMonthView = calendarState.showMonthView;

    return Scaffold(
      backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(title: 'Calendar', showBackButton: false),
      body: SafeArea(
        child: Column(
          children: [
            _buildMonthToggle(selectedDate),
            // Scrollable date row with indicator bar
            _buildScrollableDateRow(context, selectedDate),
            SizedBox(height: 8),

            // Month calendar view (conditionally shown)
            if (showMonthView) _buildScrollableMonthView(context, selectedDate),

            // Show either day view or agenda view based on selection
            if (calendarState.isLoading)
              const Expanded(child: Center(child: CircularProgressIndicator()))
            else if (calendarState.error != null)
              Expanded(
                child: Center(child: Text('Error: ${calendarState.error}')),
              )
            else if (selectedView == 'Agenda')
              Expanded(child: AgendaScreen(selectedDate: selectedDate))
            else
              Expanded(child: DayScreen(selectedDate: selectedDate)),
          ],
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(onTap: (index) {}),
    );
  }

  Widget _buildMonthToggle(DateTime selectedDate) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DmSansText(
            DateFormat('MMMM, yyyy').format(selectedDate),
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.viewColor,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildToggleButton('Agenda'),
              const SizedBox(width: 8),
              _buildToggleButton('Day'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildToggleButton(String label) {
    final bool isSelected = selectedView == label;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedView = label;
        });
      },
      child: Container(
        width: 84,
        height: 28,
        decoration: BoxDecoration(
          color: isSelected ? AppColors.viewColor : AppColors.whiteColor,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(color: AppColors.lightGreyColor2),
        ),
        alignment: Alignment.center,
        child: OpenSansText(
          label,
          color: isSelected ? AppColors.whiteColor : AppColors.viewColor,
          fontWeight: FontWeight.w400,
          fontSize: 12,
        ),
      ),
    );
  }

  // Scrollable Date Row Widget
  Widget _buildScrollableDateRow(BuildContext context, DateTime selectedDate) {
    return Consumer(
      builder: (context, ref, _) {
        final isExpanded = ref.watch(graphCalendarProvider).showMonthView;

        final today = DateTime.now();
        final startDate = today.subtract(const Duration(days: 60));

        // Calculate index offset to scroll directly to selected date
        final selectedIndex = selectedDate.difference(startDate).inDays;

        final scrollController = ScrollController();

        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (scrollController.hasClients && selectedIndex >= 0) {
            const itemWidth = 56.0;
            final screenWidth = MediaQuery.of(context).size.width;
            final targetPosition =
                (selectedIndex * itemWidth) -
                (screenWidth / 2) +
                (itemWidth / 2);

            scrollController.jumpTo(targetPosition.clamp(0.0, double.infinity));
          }
        });

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Divider(
              height: 1,
              thickness: 0.5,
              color: AppColors.calenderdivColor,
            ),
            Container(
              color: AppColors.whiteColor,
              height: 92,
              padding: const EdgeInsets.only(top: 12, bottom: 4),
              child: Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                      controller: scrollController,
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      itemBuilder: (context, index) {
                        final date = startDate.add(Duration(days: index));

                        return Padding(
                          padding: EdgeInsets.only(
                            left: index == 0 ? 16.0 : 8.0,
                            right: 8.0,
                          ),
                          child: _buildDateItem(date, selectedDate, (selected) {
                            ref
                                .read(graphCalendarProvider.notifier)
                                .selectDate(selected);
                          }),
                        );
                      },
                      itemCount: null, // Infinite scrolling
                    ),
                  ),
                  const SizedBox(height: 6),
                  if (!isExpanded)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 1),
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          ref
                              .read(graphCalendarProvider.notifier)
                              .toggleCalendarView();
                        },
                        onVerticalDragUpdate: (details) {
                          ref
                              .read(graphCalendarProvider.notifier)
                              .toggleCalendarView();
                        },
                        child: Container(
                          width: 38,
                          height: 4,
                          decoration: BoxDecoration(
                            color: AppColors.blackColor.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const Divider(
              height: 1,
              thickness: 0.5,
              color: AppColors.calenderdivColor,
            ),
          ],
        );
      },
    );
  }

  // Scrollable Month Calendar View with responsive height - fixed for iPhone SE
  Widget _buildScrollableMonthView(
    BuildContext context,
    DateTime selectedDate,
  ) {
    final monthsToShow = 12;
    var startMonth = DateTime(
      DateTime.now().year,
      DateTime.now().month - monthsToShow ~/ 2,
      1,
    );

    int selectedMonthIndex = -1;
    for (int i = 0; i < monthsToShow; i++) {
      final currentMonth = DateTime(startMonth.year, startMonth.month + i, 1);
      if (currentMonth.year == selectedDate.year &&
          currentMonth.month == selectedDate.month) {
        selectedMonthIndex = i;
        break;
      }
    }

    if (selectedMonthIndex == -1) {
      startMonth = DateTime(
        selectedDate.year,
        selectedDate.month - (monthsToShow ~/ 2),
        1,
      );
      selectedMonthIndex = monthsToShow ~/ 2;
    }

    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    double heightPercentage;
    if (screenWidth <= 375 && screenHeight <= 667) {
      heightPercentage = 0.285;
    } else if (screenWidth <= 375) {
      heightPercentage = 0.32;
    } else {
      heightPercentage = 0.38;
    }

    return Consumer(
      builder: (context, ref, _) {
        final isExpanded = ref.watch(graphCalendarProvider).showMonthView;

        return Container(
          height: screenHeight * heightPercentage - 40,
          width: double.infinity,
          decoration: const BoxDecoration(
            color: AppColors.secondaryColor,
            border: Border(
              bottom: BorderSide(color: AppColors.calanderbordercolor),
            ),
          ),
          child: Column(
            children: [
              Expanded(
                child: PageView.builder(
                  controller: PageController(
                    initialPage: selectedMonthIndex,
                    viewportFraction: 1.0,
                  ),
                  physics: const BouncingScrollPhysics(),
                  itemCount: monthsToShow,
                  itemBuilder: (context, monthIndex) {
                    final currentMonth = DateTime(
                      startMonth.year,
                      startMonth.month + monthIndex,
                      1,
                    );
                    return _buildMonthGrid(
                      context,
                      currentMonth,
                      selectedDate,
                      isExpanded,
                    );
                  },
                ),
              ),

              // Show drag handle only if calendar is not expanded
              if (!isExpanded)
                Padding(
                  padding: const EdgeInsets.only(bottom: 1),
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      ref
                          .read(graphCalendarProvider.notifier)
                          .toggleCalendarView();
                    },
                    onVerticalDragUpdate: (details) {
                      _dragOffset += details.delta.dy;

                      // Only react to downward drag
                      if (_dragOffset > 30) {
                        ref
                            .read(graphCalendarProvider.notifier)
                            .toggleCalendarView();
                        _dragOffset = 0;
                      }
                    },
                    onVerticalDragEnd: (_) {
                      _dragOffset = 0;
                    },
                    child: Container(
                      width: 38,
                      height: 4,
                      decoration: BoxDecoration(
                        color: AppColors.draggablebarcolor,
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  // Helper to build a single month grid - optimized for iPhone SE
  Widget _buildMonthGrid(
    BuildContext context,
    DateTime month,
    DateTime selectedDate,
    bool isExpanded,
  ) {
    final today = DateTime.now();
    final firstDayOfMonth = DateTime(month.year, month.month, 1);

    // Get the day of week for the first day (0 = Sunday, 6 = Saturday)
    final firstWeekdayOfMonth = firstDayOfMonth.weekday % 7;

    // Calculate the first day to show (might be from previous month)
    final firstDayToShow = firstDayOfMonth.subtract(
      Duration(days: firstWeekdayOfMonth),
    );

    // Generate dates for 5 weeks only (35 days)
    const totalDaysToShow = 35;
    final allDatesToShow = List.generate(
      totalDaysToShow,
      (index) => firstDayToShow.add(Duration(days: index)),
    );

    // Group dates by week (5 weeks)
    final weeks = <List<DateTime>>[];
    for (var i = 0; i < allDatesToShow.length; i += 7) {
      weeks.add(allDatesToShow.sublist(i, min(i + 7, allDatesToShow.length)));
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children:
                  weeks.map((weekDates) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children:
                          weekDates.map((date) {
                            final isCurrentMonth = date.month == month.month;
                            final isFirstDayOfMonth = date.day == 1;
                            final monthName = DateFormat('MMM').format(date);
                            final isSelected =
                                date.year == selectedDate.year &&
                                date.month == selectedDate.month &&
                                date.day == selectedDate.day;
                            final isToday =
                                date.year == today.year &&
                                date.month == today.month &&
                                date.day == today.day;

                            return Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  ref
                                      .read(graphCalendarProvider.notifier)
                                      .selectDate(date);
                                  ref
                                      .read(graphCalendarProvider.notifier)
                                      .toggleCalendarView();
                                },
                                child: Container(
                                  margin: const EdgeInsets.all(2),
                                  padding: const EdgeInsets.all(5),
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    color:
                                        isSelected
                                            ? AppColors.selecteddatecolor
                                            : Colors.transparent,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (isFirstDayOfMonth)
                                        OpenSansText(
                                          monthName,
                                          fontSize: 10,
                                          color: AppColors.blackColor,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      OpenSansText(
                                        date.day.toString(),
                                        fontSize: 14,
                                        color:
                                            !isCurrentMonth
                                                ? AppColors.draggablebarcolor
                                                : isSelected
                                                ? Colors.white
                                                : Colors.black,
                                        fontWeight:
                                            isSelected || isToday
                                                ? FontWeight.bold
                                                : FontWeight.normal,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                    );
                  }).toList(),
            ),
          ),

          // Drag handle inside grid when expanded
          if (isExpanded)
            GestureDetector(
              behavior: HitTestBehavior.translucent, // expands hit area
              onTap: () {
                ref.read(graphCalendarProvider.notifier).toggleCalendarView();
              },
              onVerticalDragUpdate: (details) {
                _dragOffset += details.delta.dy;

                if (_dragOffset < -30) {
                  // more sensitive upward drag
                  ref.read(graphCalendarProvider.notifier).toggleCalendarView();
                  _dragOffset = 0;
                }
              },
              onVerticalDragEnd: (_) {
                _dragOffset = 0;
              },
              child: Container(
                padding: const EdgeInsets.only(top: 16, bottom: 2),
                alignment: Alignment.center,
                child: Container(
                  width: 38,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.blackColor.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDateItem(
    DateTime date,
    DateTime selectedDate,
    Function(DateTime) onDateSelected,
  ) {
    final isSelected =
        date.year == selectedDate.year &&
        date.month == selectedDate.month &&
        date.day == selectedDate.day;

    final isToday = _isToday(date);

    // Get the day of week
    final dayOfWeek = DateFormat('E').format(date)[0];

    return GestureDetector(
      onTap: () => onDateSelected(date),
      child: Container(
        width: 34,
        height: 56, // Fixed height for consistent sizing
        decoration: BoxDecoration(
          color: isSelected ? AppColors.viewColor : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border:
              isToday
                  ? Border.all(
                    color:
                        isSelected ? Colors.transparent : AppColors.blackColor,
                    width: 1,
                  )
                  : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            OpenSansText(
              dayOfWeek,
              color:
                  isSelected ? AppColors.secondaryColor : AppColors.blackColor,
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
            const SizedBox(height: 16),
            OpenSansText(
              date.day.toString(),
              color:
                  isSelected ? AppColors.secondaryColor : AppColors.lightBlack,
              fontWeight: FontWeight.w400,
              fontSize: 20,
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }
}

// Helper function to get minimum of two values
int min(int a, int b) {
  return a < b ? a : b;
}
