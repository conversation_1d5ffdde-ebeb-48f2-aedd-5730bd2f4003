import 'package:flutter/material.dart';
import 'package:seawork/components/form/bottomSheet.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customBlueButton.dart';
import 'package:seawork/components/widget/customOutlineButton.dart';

import 'package:seawork/screens/parent/stories/components/advancedSettings.dart';
import 'package:seawork/screens/parent/stories/components/audienceBottomSheet.dart';
import 'package:seawork/screens/parent/stories/components/childrenBottomSheet.dart';
import 'package:seawork/screens/parent/stories/components/imageSelector.dart';
import 'package:seawork/screens/parent/stories/components/tagBottomSheet.dart';

import 'package:seawork/utils/style/colors.dart';

void main() => runApp(MaterialApp(home: StoriesScreen()));

class StoriesScreen extends StatefulWidget {
  final bool hideRecentsSection;

  const StoriesScreen({Key? key, this.hideRecentsSection = false})
      : super(key: key);

  @override
  State<StoriesScreen> createState() => _StoriesScreenState();
}

class _StoriesScreenState extends State<StoriesScreen> {
  String? selectedAudienceType;
  List<Map<String, String>> selectedChildren = [];
    List<Map<String, String>> selectedTags = [];
    Set<String> previouslySelected = {};
  @override
  Widget build(BuildContext context) {
    return Scaffold(
         backgroundColor: AppColors.secondaryColor,
      appBar: CustomAppBar(title: 'Stories'),
      
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
          ImageSelectorWidget(hideRecents: true),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: AppColors.inputfillColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Add a caption...',
                    hintStyle: TextStyle(
                    fontFamily: 'OpenSans',
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                    color: AppColors.meetbottomsheethinttextcolor, 
                  ),
                  border: InputBorder.none,
                ),
                maxLines: 3,
              ),
            ),
            SizedBox(height: 24),
            Align(
              alignment: Alignment.centerLeft,
              child: DMSans600Large(
                18,
                'More settings',AppColors.blackColor
           
              ),
            ),
            SizedBox(height: 12),
            Container(
              decoration: BoxDecoration(
                color: AppColors.whiteColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
    _buildSettingItem(
        SvgImage24x24('assets/images/eye_icon.svg'),
        'Audience',
        () {
             showAudienceBottomSheet(context, (type) {
            setState(() {
              selectedAudienceType = type;
            });
            });
        },
          trailingText: selectedAudienceType,
      ),
             _buildSettingItem(
                    SvgImage24x24('assets/images/children.svg'),
                    'Children',
                    () async {
                      final result = await showModalBottomSheet<List<Map<String, String>>>(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) {
                          return DraggableScrollableSheet(
                            initialChildSize: 0.6,
                            minChildSize: 0.4,
                            maxChildSize: 0.9,
                            expand: false,
                            builder: (context, scrollController) {
                              return Container(
                                decoration: BoxDecoration(
                                  color: AppColors.whiteColor,
                                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                                ),
                                child: ChildrenBottomSheet(preselectedChildren: selectedChildren),
                              );
                            },
                          );
                        },
                      );

                      if (result != null) {
                        setState(() {
                          selectedChildren = result;
                        });
                      }
                    },
                    trailingWidget: selectedChildren.isNotEmpty
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            children: selectedChildren
                                .map((child) => Padding(
                                      padding: const EdgeInsets.only(left: 4),
                                      child: CircleAvatar(
                                        radius: 12,
                                        backgroundImage: AssetImage(child['image']!),
                                      ),
                                    ))
                                .toList(),
                          )
                        : null,
                  ),

                  _buildSettingItem(SvgImage24x24('assets/images/peopleadd.svg'), 'Add tag',      () async {
                                      final result = await showModalBottomSheet<List<Map<String, String>>>(
                                        context: context,
                                        isScrollControlled: true,
                                        backgroundColor: Colors.transparent,
                                        builder: (context) => TagBottomSheet(previouslySelected: previouslySelected),
                                      );
                                      if (result != null) {

                                        setState(() {
                                        previouslySelected = result.map((person) => person['image']!).toSet();
                                         selectedTags = result;
                                        });
                                      }
                                    },
                                  trailingWidget: selectedTags.isNotEmpty
                                        ? Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              ...selectedTags.take(3).map((tag) => Padding(
                                                    padding: const EdgeInsets.only(left: 4),
                                                    child: CircleAvatar(
                                                      radius: 12,
                                                      backgroundImage: AssetImage(tag['image']!), 
                                                    ),
                                                  )),
                                              if (selectedTags.length > 3)
                                                Padding(
                                                  padding: const EdgeInsets.only(left: 4),
                                                  child: OpenSans600Large(12,
                                                    '+${selectedTags.length - 3}',AppColors.viewColor,
                                                  ),
                                                ),
                                            ],
                                          )
                                        : null,
                                  ),
                  _buildSettingItem(SvgImage24x24('assets/images/settings.svg'), 'Advance settings',  () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => AdvanceSettingsSheet(),
                    );
                  },),
                ],
              ),
            ),
            SizedBox(height: 32),
       
          ],
        ),
      ),
       bottomNavigationBar: Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SubmitRequestButton(
              text: "Publish",
              onPressed: () {
             
              },
            ),
            const SizedBox(height: 12),
            Customoutlinebutton(
              text: "Save as draft",
              onPressed: () {
            
              },
            ),
          ],
        ),
      ),
    );
  }
 

   Widget _buildSettingItem(
    Widget leadingIcon,
    String title,
    VoidCallback onTap, {
    String? trailingText,
      Widget? trailingWidget,
  }) {
    return ListTile(
      leading: leadingIcon,
      title: OpenSans600Large(14, title, AppColors.blackColor),
      trailing: trailingWidget ??
          (trailingText != null
              ? OpenSans400Large(12, trailingText, AppColors.blackColor)
              : null),
      onTap: onTap,
    );
  }
}



