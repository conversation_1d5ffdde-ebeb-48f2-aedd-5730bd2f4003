import 'package:flutter/material.dart';

class AppColors {
  static const primaryColor = Color(0xFF02949D);
  static const secondaryColor = Color(0xFFF7FDFF);
  static const blackColor = Color(0xFF000000);
  static const whiteColor = Color(0xFFFFFFFF);
  static const dividerColor = Color(0xFFD6DFDE);
  static const darkGreen = Color(0xFF4C841A);
  static const calenderdivColor = Color(0xFF9BC7E7);
  static const calenderDaydivColor = Color(0xFFCED2D4);
  static const calendermeetColor = Color(0xFFD0E3EA);
  static const calendermeetborderColor = Color(0xFFD6D7DE);
  static const navshadowColor = Color(0xFFC1D7E8);
  static const tcktdetailsdividerColor = Color(0xFFE3E3E3);
  static const ticketdividerColor = Color(0xFF9BC7E7);
  static const shadowColor = Color(0x809BC7E7);
  static const textColor = Color(0xFF062540);
  static const tealColor = Color(0xFF2C98A7);
  static const headingColor = Color(0xFF062540);
  static const lightBlack = Color(0xFF3C3C3C);
  static const viewColor = Color(0xFF395062);
  static const yellowColor = Color(0xFFD8B84B);
  static const lightGreen = Color(0xFF43B89C);
  static const lightGreyColor = Color(0xFF878A9E);
  static const lightGreenColor = Color(0xFF7DB64B);
  static const lightGreyColor2 = Color(0xFFCDD6DC);
  static const lightGreyshade = Color(0xFFA0B0BA);
  static const goldenColor = Color(0xFFEB9F49);
  static const bottomNavColor = Color(0xFF999999);
  static const Orange = Color(0xFFFF6332);
  static const red = Color(0xFFFF0000);
  static const message1 = Color(0xFF9A9999);
  static const subheadingcolor = Color(0xFF646464);
  static const searchfieldtext = Color(0xFF878A95);
  static const shadowBoxColor = Color(0xFF80CAD6);
  static const semitransparentBlack = Color(0x73000000);
  static const inactivecolor = Color(0xFFC4D9E8);
  static const lightgraygreen = Color(0xFFD6DFDE);
  static const dashboardheading = Color(0xFF060606);
  static const cardshadow = Color(0x20CAD640);
  static const dimColor = Color(0xFF272727);
  static const greenaccentColor = Color(0x7777EFB5);
  static const checkmarkgreen = Color(0xFF86BE45);
  static const lightGreyColor3 = Color(0xFFD4DEE3);
  static const lightGreyColor4 = Color(0xFFAAAAAA);
  static const inputfillColor = Color(0xFFE8F0F6);
  static const boxshadow = Color(0x4080CAD6);
  static const color = Color(0xFF111827);
  static const countryw = Color(0xFFE5E7EB);
  static const statuscolour = Color(0xFFCDECF4);
  static const statuscolour2 = Color(0xFFC6FDF0);
  static const statuscolour3 = Color(0xFFFFF0BD);
  static const mediumGreyColor = Color(0xFF9E9E9E);
  static const darkGreyColor = Color(0xFF575555);
  static const darkGreylinkColor = Color(0xFF574444);
  static const draggablebarcolor = Color(0xFFBDBDBD);
  static const calanderbordercolor = Color(0xFFE0E0E0);
  static const calandertimelinecolor = Color(0xFF525252);
  static const calanderbordercolor2 = Color(0xFFEEEEEE);
  static const selecteddatecolor = Color(0xFF2C3E50);
  static const meetingdurationcolor = Color(0xFF212525);
  static const meetingdetailsbuttoncolor = Color(0xFF32506D);
  static const meetbottomsheethinttextcolor = Color(0XFFA0B0BA);
  static const darkBlackColor = Color(0xFF111111);
  static const mediumDarkGreyColor = Color(0xFF5C5C5C);
  static const lightBlueBackgroundColor = Color(0xFFEBF2FD);
  static const boxshadowcolor = Color(0xFF80CAD6);
  static const transparentColor = Color(0x00000000);
  static const opaqueCyan = Color(0xFFbcf7fc);
  static const profiledotcolor = Color(0xFF0AC1CC);
  static const searhbartext = Color(0xFFE8F0F6);
  static const darkgrey = Color(0xFF3C3C3C);
  static const cardbordercolor = Color(0xFF69B1B6);
  static const editcolor = Color(0xFFEBEFF1);
  static const editingcolor = Color(0xFFCFDBEF);
  static const peoplecalanderheadingcolor = Color(0XFF0F2552);
  static const peoplecalanderweekcolor = Color(0xFF7E818C);
  static const bdaybannershadowcolor = Color(0xFF80CAD6);
  static const bdaycirclecolor = Color(0xFF94C6FF);
  static const bdaycirclecolor2 = Color(0xFFE6F4F1);
  static const bdaybannercolor1 = Color(0xFF36B7C6);
  static const bdaybannercolor2 = Color(0xFFE203A3);
  static const bdaybannercolor3 = Color(0xFF3E98FF);
  static const bdaypersonlocationcolor = Color(0xFF8c8c8c);
  static const peopleboxshadow = Color(0xFFe9f7fa);
  static const customBlueColor = Color(0xFF3E98FF);
  static const lightGreyColorShade = Color(0xFFD9D9D9);
  static const goldenYellow = Color(0xFFE2A900);
  static const declineRed = Color(0xFFFD3C00);
  static const seaGreen = Color(0xFF08AA78);
  static const softPurple = Color(0xFF7C86C6);
  static const boxbgcolor = Color(0xFFD8ECF3);
  static const chevroncolor = Color(0xFF848A95);
  static const dividercolor = Color(0xFFE4E5E7);
  static const amberBrownColor = Color(0xFFA36700);
  static const grey = Color(0xFF707B82);
  static const nodatarcolor = Color(0xFFA6B4B6);
  static const framecolor = Color(0xFF35EAF6);
  static const framecolor2 = Color(0xFF81A5CE);
  static const blue = Color(0xFF93C8E2);
  static const lightBlackColor = Color(0xFF3A3A3A);
  static const lightBlueColor = Color(0xFFE4F3FC);
  static const lightBlueShade = Color(0xFFD9E4ED);
  static const goldenShade = Color(0xFFB38600);
  static const lightYellowShade = Color(0xFFFFF3D1);
  static const brightBlue = Color(0xFF30B9F9);
  static const darkOrange = Color(0xFFFC5B31);
  static const lightBlueStatus = Color(0xFFC1E6FF);
  static const primaryBlue = Color(0xFF1F82C3);
  static const purple = Color(0xFF5704AB);
  static const lightPurple = Color(0xFFDDDEFF);
  static const green = Color(0xFF3D8C05);
  static const lightGreenShade = Color(0xFFD5FFB7);
  static const redColor = Color(0xFFB82B00);
  static const lightRedShade = Color(0xFFFFDACE);
  static const dividerColor1 = Color(0xFFD2E4F1);
  static const borderColor = Color(0xFFDEDEDE);
  static const lightGrey = Color(0xFF90A6BB);
  static const lightBlue = Color(0xFFEFF8FF); 
  static const mediumBlue = Color(0xFF558EBB); 
  static const microinteraction = Color(0xFFD3DDE3);
  static const formToast = Color(0xFFB8D6E9);// dont know exact, solved to build issue
  static const formBoxDecorationColor = Color(0xFFF7FDFF);// dont know exact, solved to build issue
  static const formBoxDecorationColor2 = Color(0xFFF7FDFF);// dont know exact, solved to build issue
  static const infoColor = Color(0xFF0053B3);

}

// }
