import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/utils/style/colors.dart';

class CardSectionTwo extends StatefulWidget {
  final String title;
  final double? height;
  final String subtitle;
  final Widget? icon;
  final List<Widget>? children;
  final VoidCallback? onTap;
  final bool showTrailing;
  final bool isSubtitleRed;
  final Color? borderShade;
  final Color? shadowColor;
  final bool isSelectable;
  final String? selectedCard;
  final Function(String)? onCardSelected;
  final String trailingArrowType;

  const CardSectionTwo({
    Key? key,
    required this.title,
    required this.subtitle,
    this.height,
    this.icon,
    this.children,
    this.onTap,
    this.showTrailing = true,
    this.isSubtitleRed = false,
    this.borderShade,
    this.shadowColor,
    this.isSelectable = false,
    this.selectedCard,
    this.onCardSelected,
    this.trailingArrowType = "down_up",
  }) : super(key: key);

  @override
  _CardSectionTwoState createState() => _CardSectionTwoState();
}

class _CardSectionTwoState extends State<CardSectionTwo> {
  bool _isExpanded = false;

  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });

    if (widget.isSelectable && widget.onCardSelected != null) {
      widget.onCardSelected!(widget.title);
    }

    if (widget.onTap != null) {
      widget.onTap!();
    }
  }

  Widget _buildTrailingWidget() {
    switch (widget.trailingArrowType) {
      case "down_up":
        return _isExpanded
            ? Transform.rotate(
                angle: 3.14159, // 180 degrees (points up)
                child: CustomSvgImage(
                  imageName: "svgdownarrow",
                  color: AppColors.viewColor,
                  height: 24,
                  width: 24,
                ),
              )
            : CustomSvgImage(
                imageName: "svgdownarrow", // Default points down
                color: AppColors.viewColor,
                height: 24,
                width: 24,
              );
      case "right":
        return CustomSvgImage(
          imageName: "rightarrow",
          color: AppColors.viewColor,
          height: 24,
          width: 24,
        );
      case "right_up":
      default:
        return _isExpanded
            ? Transform.rotate(
                angle: -1.5708, // -90 degrees (points up)
                child: CustomSvgImage(
                  imageName: "rightarrow",
                  color: AppColors.viewColor,
                  height: 24,
                  width: 24,
                ),
              )
            : Transform.rotate(
                angle: 1.5708, // 90 degrees (points down)
                child: CustomSvgImage(
                  imageName: "rightarrow",
                  color: AppColors.viewColor,
                  height: 24,
                  width: 24,
                ),
              );
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isSelected = widget.selectedCard == widget.title;

    return Card(
      color: isSelected ? AppColors.lightGreyColor2 : AppColors.whiteColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: widget.borderShade != null
            ? BorderSide(color: widget.borderShade!, width: 1)
            : BorderSide.none,
      ),
      elevation: 0,
      shadowColor: widget.shadowColor,
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: Column(
          children: [
            InkWell(
              onTap: _toggleExpansion,
              child: Container(
                height: 70, // Set the height of the card to 70

                child: ListTile(
                  leading: widget.icon,
                  title: DMSans700Large(16,
                    widget.title,
                    AppColors.blackColor,
                  ),
                  subtitle: OpenSansText(
                    widget.subtitle,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: widget.isSubtitleRed
                        ? AppColors.Orange
                        : AppColors.blackColor,
                    letterSpacing: 0,
                  ),
                  trailing: widget.showTrailing
                      ? _buildTrailingWidget()
                      : const SizedBox.shrink(),
                ),
              ),
            ),
            if (_isExpanded && widget.children != null) ...widget.children!,
          ],
        ),
      ),
    );
  }
}
