import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../models/mentioned_user.dart';

/// Responsive bottom sheet for displaying mentioned users and nurseries
/// Uses no Positioned/Stack layouts for better device compatibility
class MentionUsersAndNurseriesSheet extends StatelessWidget {
  final List<MentionedUser> users;
  
  const MentionUsersAndNurseriesSheet({super.key, required this.users});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        // at most 70% of the screen height – still scrollable
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // drag-handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: const Color(0xFF395062),
              borderRadius: BorderRadius.circular(9),
            ),
          ),
          // Title
          // const Padding(
          //   padding: EdgeInsets.only(bottom: 16),
          //   child: Text(
          //     'Mentioned Users',
          //     style: TextStyle(
          //       fontFamily: 'Open Sans',
          //       fontSize: 18,
          //       fontWeight: FontWeight.w600,
          //       color: Colors.black,
          //     ),
          //   ),
          // ),
          // List of users
          Expanded(
            child: ListView.separated(
              itemCount: users.length,
              separatorBuilder: (_, __) => const SizedBox(height: 4),
              itemBuilder: (context, index) {
                final user = users[index];
                return _MentionedRow(user: user);
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// Individual row widget for each mentioned user/nursery
class _MentionedRow extends StatelessWidget {
  final MentionedUser user;
  
  const _MentionedRow({required this.user});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 64,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 3,
            offset: Offset(0, 1),
          )
        ],
      ),
      child: Row(
        children: [
          // avatar with appropriate icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: user.type == MentionUserType.student ? Colors.lightBlue[100] : Colors.green[100],
              borderRadius: BorderRadius.circular(40),
            ),
            child: Center(
              child: SvgPicture.asset(
                user.type == MentionUserType.student
                    ? 'assets/images/students_stories.svg'
                    : 'assets/images/nurseries_stories.svg',
                width: 24,
                height: 24,
                // Use a color that contrasts with the background
                color: user.type == MentionUserType.student ? Colors.blue[800] : Colors.green[800],
              ),
            ),
          ),
          const SizedBox(width: 12),
          // name with overflow handling
          Expanded(
            child: Text(
              user.name,
              style: const TextStyle(
                fontFamily: 'Open Sans',
                fontSize: 14,
                height: 1.2,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
