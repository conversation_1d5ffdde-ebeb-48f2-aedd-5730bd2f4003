import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApplicationModel } from '../providers/models/application/application.model';
import { ApplicationPreferenceModel } from '../providers/models/application/applicationPreference.model';
import { ApplicationReviewMVModel } from '../providers/models/application/applicationReviewMV.model';
import { ApplicationStatusModel } from '../providers/models/application/applicationStatus.model';
import { ApplicationViewModelModel } from '../providers/models/application/applicationViewModel.model';
import { UpdatePendingApplicationsVMModel } from '../providers/models/application/updatePendingApplicationsVM.model';
import { VerificationReAssignmentModel } from '../providers/models/application/verificationReAssignment.model';
import { ApplicationService } from '../providers/services/application.service';
@Controller()
export class ApplicationController {
  constructor(private readonly service: ApplicationService) {}

  @Post('application/addUpdateApplication')
  public async addUpdateApplication(
    @Body() body: ApplicationModel,
  ): Promise<ApplicationModel> {
    // -- not used
    const response = await this.service.addUpdateApplication(body);
    return response;
  }

  @Get('application/getApplicationsByParentId')
  public async getApplicationsByParentId(
    parentId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getApplicationsByParentId(parentId);
    return response;
  }

  @Get('application/getApplicationsByReviwerId')
  public async getApplicationsByReviewerId(
    pageNumber?: number,
    pageSize?: number,
    code?: string,
    reviewerId?: number,
    fullNameInEnglish?: string,
    applicationStatusId?: number,
    selAcademicYearId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getApplicationsByReviewerId(
      pageNumber,
      pageSize,
      code,
      reviewerId,
      fullNameInEnglish,
      applicationStatusId,
      selAcademicYearId,
    );
    return response;
  }

  @Get('application/getApplicationsBySecondReviwerId')
  public async getApplicationsBySecondReviewerId(
    pageNumber?: number,
    pageSize?: number,
    code?: string,
    reviewerId?: number,
    fullNameInEnglish?: string,
    applicationStatusId?: number,
    selAcademicYearId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getApplicationsBySecondReviewerId(
      pageNumber,
      pageSize,
      code,
      reviewerId,
      fullNameInEnglish,
      applicationStatusId,
      selAcademicYearId,
    );
    return response;
  }

  @Post('application/deleteApplication')
  public async deleteApplication(
    @Body() body: ApplicationModel,
  ): Promise<ApplicationModel> {
    // -- not used
    const response = await this.service.deleteApplication(body);
    return response;
  }

  @Post('application/updateApplicationNurseryPreference')
  public async updateApplicationNurseryPreference(
    @Body() body: ApplicationPreferenceModel,
  ): Promise<ApplicationPreferenceModel> {
    // -- not used
    const response =
      await this.service.updateApplicationNurseryPreference(body);
    return response;
  }

  @Get('application/getNurserySuggesionsByKidInfoId')
  public async getNurserySuggestionsByKidInfoId(
    kidInfoId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response =
      await this.service.getNurserySuggestionsByKidInfoId(kidInfoId);
    return response;
  }

  @Get('application/cancelApplication')
  public async cancelApplication(
    @Query('applicationId') applicationId: number,
    @Query('isDeleteReviewHistory') isDeleteReviewHistory: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.service.cancelApplication(
      applicationId,
      isDeleteReviewHistory,
    );
    return response;
  }

  @Get('application/getApplicationForReviewById')
  public async getApplicationForReviewById(
    applicationId?: number,
  ): Promise<ApplicationReviewMVModel> {
    // -- not used
    const response =
      await this.service.getApplicationForReviewById(applicationId);
    return response;
  }

  @Post('application/updateApplicationStatus')
  public async updateApplicationStatus(
    @Body() body: ApplicationStatusModel,
  ): Promise<ApplicationStatusModel> {
    // -- not used
    const response = await this.service.updateApplicationStatus(body);
    return response;
  }

  @Get('application/getRevertedSectionsByParentId')
  public async getRevertedSectionsByParentId(
    parentId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getRevertedSectionsByParentId(parentId);
    return response;
  }

  @Get('application/getAllParentEditableSections')
  public async getAllParentEditableSections(
    parentId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getAllParentEditableSections(parentId);
    return response;
  }

  @Post('application/updateApplicationReview')
  public async updateApplicationReview(
    @Body() body: ApplicationReviewMVModel,
  ): Promise<ApplicationReviewMVModel> {
    // -- not used
    const response = await this.service.updateApplicationReview(body);
    return response;
  }

  @Get('application/getAllottedApplicationsForAdmin')
  public async getAllottedApplicationsForAdmin(
    pageNumber?: number,
    pageSize?: number,
    allotmentCreatedDateFrom?: string,
    allotmentCreatedDateTo?: string,
    appliedDateFrom?: string,
    appliedDateTo?: string,
    enrolledDateFrom?: string,
    enrolledDateTo?: string,
    lastUpdatedFrom?: string,
    lastUpdatedTo?: string,
    code?: string,
    reviewer?: string,
    fullNameInEnglish?: string,
    fatherName?: string,
    motherName?: string,
    phoneNumber?: string,
    nurseryId?: number,
    nurseryPrefId1?: number,
    nurseryPrefId2?: number,
    nurseryPrefId3?: number,
    childCode?: string,
    applicationStatusId?: number,
    admissionStatusId?: number,
    selAcademicYearId?: number,
    allottedNurseryId?: number,
    verifier?: string,
    displayStatus?: string,
    reasonForCancellation?: string,
    hasSiblings?: string,
    fatherNationality?: string,
    fatherEmployerTypeId?: number,
    fatherEmployerName?: string,
    fatherIssuingAuthority?: string,
    fatherIsWorking?: boolean,
    fatherDisabilityStatus?: boolean,
    motherNationality?: string,
    motherEmployerTypeId?: number,
    motherEmployerName?: string,
    motherIssuingAuthority?: string,
    motherIsWorking?: boolean,
    motherDisabilityStatus?: boolean,
    gradeId?: number,
    reviewerName?: string,
    verifierName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getAllottedApplicationsForAdmin(
      pageNumber,
      pageSize,
      allotmentCreatedDateFrom,
      allotmentCreatedDateTo,
      appliedDateFrom,
      appliedDateTo,
      enrolledDateFrom,
      enrolledDateTo,
      lastUpdatedFrom,
      lastUpdatedTo,
      code,
      reviewer,
      fullNameInEnglish,
      fatherName,
      motherName,
      phoneNumber,
      nurseryId,
      nurseryPrefId1,
      nurseryPrefId2,
      nurseryPrefId3,
      childCode,
      applicationStatusId,
      admissionStatusId,
      selAcademicYearId,
      allottedNurseryId,
      verifier,
      displayStatus,
      reasonForCancellation,
      hasSiblings,
      fatherNationality,
      fatherEmployerTypeId,
      fatherEmployerName,
      fatherIssuingAuthority,
      fatherIsWorking,
      fatherDisabilityStatus,
      motherNationality,
      motherEmployerTypeId,
      motherEmployerName,
      motherIssuingAuthority,
      motherIsWorking,
      motherDisabilityStatus,
      gradeId,
      reviewerName,
      verifierName,
    );
    return response;
  }

  @Get('application/getAllottedApplicationsForAdminNotOptimed')
  public async getAllottedApplicationsForAdminNotOptimized(
    pageNumber?: number,
    pageSize?: number,
    allotmentCreatedDateFrom?: string,
    allotmentCreatedDateTo?: string,
    appliedDateFrom?: string,
    appliedDateTo?: string,
    enrolledDateFrom?: string,
    enrolledDateTo?: string,
    lastUpdatedFrom?: string,
    lastUpdatedTo?: string,
    code?: string,
    reviewer?: string,
    fullNameInEnglish?: string,
    fatherName?: string,
    motherName?: string,
    phoneNumber?: string,
    nurseryId?: number,
    nurseryPrefId1?: number,
    nurseryPrefId2?: number,
    nurseryPrefId3?: number,
    childCode?: string,
    applicationStatusId?: number,
    admissionStatusId?: number,
    selAcademicYearId?: number,
    allottedNurseryId?: number,
    verifier?: string,
    displayStatus?: string,
    reasonForCancellation?: string,
    hasSiblings?: string,
    fatherNationality?: string,
    fatherEmployerTypeId?: number,
    fatherEmployerName?: string,
    fatherIssuingAuthority?: string,
    fatherIsWorking?: boolean,
    fatherDisabilityStatus?: boolean,
    motherNationality?: string,
    motherEmployerTypeId?: number,
    motherEmployerName?: string,
    motherIssuingAuthority?: string,
    motherIsWorking?: boolean,
    motherDisabilityStatus?: boolean,
    gradeId?: number,
    reviewerName?: string,
    verifierName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response =
      await this.service.getAllottedApplicationsForAdminNotOptimized(
        pageNumber,
        pageSize,
        allotmentCreatedDateFrom,
        allotmentCreatedDateTo,
        appliedDateFrom,
        appliedDateTo,
        enrolledDateFrom,
        enrolledDateTo,
        lastUpdatedFrom,
        lastUpdatedTo,
        code,
        reviewer,
        fullNameInEnglish,
        fatherName,
        motherName,
        phoneNumber,
        nurseryId,
        nurseryPrefId1,
        nurseryPrefId2,
        nurseryPrefId3,
        childCode,
        applicationStatusId,
        admissionStatusId,
        selAcademicYearId,
        allottedNurseryId,
        verifier,
        displayStatus,
        reasonForCancellation,
        hasSiblings,
        fatherNationality,
        fatherEmployerTypeId,
        fatherEmployerName,
        fatherIssuingAuthority,
        fatherIsWorking,
        fatherDisabilityStatus,
        motherNationality,
        motherEmployerTypeId,
        motherEmployerName,
        motherIssuingAuthority,
        motherIsWorking,
        motherDisabilityStatus,
        gradeId,
        reviewerName,
        verifierName,
      );
    return response;
  }

  @Get('application/getApplicationsForAdmin')
  public async getApplicationsForAdmin(
    pageNumber?: number,
    pageSize?: number,
    appliedDateFrom?: string,
    appliedDateTo?: string,
    lastUpdatedFrom?: string,
    lastUpdatedTo?: string,
    code?: string,
    reviewer?: string,
    fullNameInEnglish?: string,
    fatherName?: string,
    motherName?: string,
    phoneNumber?: string,
    nurseryId?: number,
    nurseryPrefId1?: number,
    nurseryPrefId2?: number,
    nurseryPrefId3?: number,
    childCode?: string,
    applicationStatusId?: number,
    admissionStatusId?: number,
    selAcademicYearId?: number,
    allottedNurseryId?: number,
    verifier?: string,
    reasonForCancellation?: string,
    hasSiblings?: string,
    fatherNationality?: string,
    fatherEmployerTypeId?: number,
    fatherIssuingAuthority?: string,
    fatherIsWorking?: boolean,
    fatherDisabilityStatus?: boolean,
    motherNationality?: string,
    motherEmployerTypeId?: number,
    motherIssuingAuthority?: string,
    motherIsWorking?: boolean,
    motherDisabilityStatus?: boolean,
    gradeId?: number,
    reviewerName?: string,
    verifierName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getApplicationsForAdmin(
      pageNumber,
      pageSize,
      appliedDateFrom,
      appliedDateTo,
      lastUpdatedFrom,
      lastUpdatedTo,
      code,
      reviewer,
      fullNameInEnglish,
      fatherName,
      motherName,
      phoneNumber,
      nurseryId,
      nurseryPrefId1,
      nurseryPrefId2,
      nurseryPrefId3,
      childCode,
      applicationStatusId,
      admissionStatusId,
      selAcademicYearId,
      allottedNurseryId,
      verifier,
      reasonForCancellation,
      hasSiblings,
      fatherNationality,
      fatherEmployerTypeId,
      fatherIssuingAuthority,
      fatherIsWorking,
      fatherDisabilityStatus,
      motherNationality,
      motherEmployerTypeId,
      motherIssuingAuthority,
      motherIsWorking,
      motherDisabilityStatus,
      gradeId,
      reviewerName,
      verifierName,
    );
    return response;
  }

  @Get('application/getApplicationsForAdminNotOptimized')
  public async getApplicationsForAdminNotOptimized(
    pageNumber?: number,
    pageSize?: number,
    lastUpdatedFrom?: string,
    lastUpdatedTo?: string,
    code?: string,
    reviewer?: string,
    fullNameInEnglish?: string,
    fatherName?: string,
    motherName?: string,
    phoneNumber?: string,
    nurseryId?: number,
    nurseryPrefId1?: number,
    nurseryPrefId2?: number,
    nurseryPrefId3?: number,
    childCode?: string,
    applicationStatusId?: number,
    admissionStatusId?: number,
    selAcademicYearId?: number,
    allottedNurseryId?: number,
    verifier?: string,
    reasonForCancellation?: string,
    hasSiblings?: string,
    fatherNationality?: string,
    fatherEmployerTypeId?: number,
    fatherIssuingAuthority?: string,
    fatherIsWorking?: boolean,
    fatherDisabilityStatus?: boolean,
    motherNationality?: string,
    motherEmployerTypeId?: number,
    motherIssuingAuthority?: string,
    motherIsWorking?: boolean,
    motherDisabilityStatus?: boolean,
    gradeId?: number,
    reviewerName?: string,
    verifierName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getApplicationsForAdminNotOptimized(
      pageNumber,
      pageSize,
      lastUpdatedFrom,
      lastUpdatedTo,
      code,
      reviewer,
      fullNameInEnglish,
      fatherName,
      motherName,
      phoneNumber,
      nurseryId,
      nurseryPrefId1,
      nurseryPrefId2,
      nurseryPrefId3,
      childCode,
      applicationStatusId,
      admissionStatusId,
      selAcademicYearId,
      allottedNurseryId,
      verifier,
      reasonForCancellation,
      hasSiblings,
      fatherNationality,
      fatherEmployerTypeId,
      fatherIssuingAuthority,
      fatherIsWorking,
      fatherDisabilityStatus,
      motherNationality,
      motherEmployerTypeId,
      motherIssuingAuthority,
      motherIsWorking,
      motherDisabilityStatus,
      gradeId,
      reviewerName,
      verifierName,
    );
    return response;
  }

  @Get('application/getAuditHistory')
  public async getAuditHistory(
    applicationId?: number,
    key?: string,
    isKid?: boolean,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getAuditHistory(
      applicationId,
      key,
      isKid,
    );
    return response;
  }

  @Get('application/updateNationalityReviewStatus')
  public async updateNationalityReviewStatus(): Promise<ApplicationModel> {
    // -- not used
    const response = await this.service.updateNationalityReviewStatus();
    return response;
  }

  @Get('application/applicationTransfertoSpecificNurseryCountbyClass')
  public async applicationTransferToSpecificNurseryCountByClass(
    nurseryIds?: string,
    name?: string,
    gradeDisplayName?: string,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response =
      await this.service.applicationTransferToSpecificNurseryCountByClass(
        nurseryIds,
        name,
        gradeDisplayName,
      );
    return response;
  }

  @Post('application/verificationReAssignment')
  public async verificationReAssignment(
    @Body() body: VerificationReAssignmentModel,
  ): Promise<VerificationReAssignmentModel> {
    // -- not used
    const response = await this.service.verificationReAssignment(body);
    return response;
  }

  @Get('application/getPreviousAcademicYearPendingApplications')
  public async getPreviousAcademicYearPendingApplications(
    parentId?: number,
  ): Promise<UpdatePendingApplicationsVMModel[]> {
    // -- not used
    const response =
      await this.service.getPreviousAcademicYearPendingApplications(parentId);
    return response;
  }

  @Post('application/updateApplicationForNewAcademicYear')
  public async updateApplicationForNewAcademicYear(
    @Body() body: UpdatePendingApplicationsVMModel,
  ): Promise<UpdatePendingApplicationsVMModel> {
    // -- not used
    const response =
      await this.service.updateApplicationForNewAcademicYear(body);
    return response;
  }

  @Get('application/previewProfileDetails')
  public async previewProfileDetails(
    kidInfoId?: number,
  ): Promise<ApplicationViewModelModel> {
    // -- not used
    const response = await this.service.previewProfileDetails(kidInfoId);
    return response;
  }

  @Get('application/getRevertAutoCancelInfo')
  public async getRevertAutoCancelInfo(
    applicationId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response = await this.service.getRevertAutoCancelInfo(applicationId);
    return response;
  }

  @Get('application/adminConsentRequest')
  public async adminConsentRequest(
    applicationId?: number,
  ): Promise<ApplicationModel> {
    // -- not used
    const response = await this.service.adminConsentRequest(applicationId);
    return response;
  }

  @Get('application/getConsentRequestedApplicationsByParentId')
  public async getConsentRequestedApplicationsByParentId(
    parentId?: number,
  ): Promise<ApplicationModel[]> {
    // -- not used
    const response =
      await this.service.getConsentRequestedApplicationsByParentId(parentId);
    return response;
  }

  @Post('application/updateMultipleApplicationConsentRequest')
  public async updateMultipleApplicationConsentRequest(
    @Body() body: ApplicationModel,
  ): Promise<ApplicationModel> {
    // -- not used
    const response =
      await this.service.updateMultipleApplicationConsentRequest(body);
    return response;
  }
}
