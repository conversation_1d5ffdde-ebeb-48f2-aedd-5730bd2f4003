import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';

class CustomConfirmationDialog extends StatelessWidget {
  final String? title;
  final String message;
  final String? primaryButtonText;
  final String? secondaryButtonText;
  final VoidCallback? onPrimaryPressed;
  final VoidCallback? onSecondaryPressed;
  final Color? primaryButtonColor;
  final Color? secondaryButtonColor;
  final Widget? customContent;
  final bool showCloseButton;

  const CustomConfirmationDialog({
    super.key,
    this.title,
    required this.message,
    this.primaryButtonText,
    this.secondaryButtonText,
    this.onPrimaryPressed,
    this.onSecondaryPressed,
    this.primaryButtonColor,
    this.secondaryButtonColor,
    this.customContent,
    this.showCloseButton = false,
  }) : assert(
         (primaryButtonText == null && onPrimaryPressed == null) ||
             (primaryButtonText != null && onPrimaryPressed != null),
         'Both primaryButtonText and onPrimaryPressed must be provided or both must be null',
       ),
       assert(
         (secondaryButtonText == null && onSecondaryPressed == null) ||
             (secondaryButtonText != null && onSecondaryPressed != null),
         'Both secondaryButtonText and onSecondaryPressed must be provided or both must be null',
       );

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.whiteColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      insetPadding: const EdgeInsets.symmetric(horizontal: 24),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (showCloseButton)
              Align(
                alignment: Alignment.topRight,
                child: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
            if (title != null) ...[
              DMSans600Medium(
                18,
                title!,
                AppColors.blackColor,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
            ],
            OpenSans400Large(
              14,
              message,
              AppColors.lightBlackColor,
              textAlign: TextAlign.center,
            ),
            if (customContent != null) ...[
              const SizedBox(height: 16),
              customContent!,
            ],
            const SizedBox(height: 24),
            if (primaryButtonText != null || secondaryButtonText != null)
              Row(
                children: [
                  if (secondaryButtonText != null)
                    Expanded(
                      child: OutlinedButton(
                        onPressed: onSecondaryPressed,
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          side: BorderSide(
                            color: secondaryButtonColor ?? AppColors.viewColor,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        child: Text(
                          secondaryButtonText!,
                          style: TextStyle(
                            color: secondaryButtonColor ?? AppColors.blackColor,
                          ),
                        ),
                      ),
                    ),
                  if (secondaryButtonText != null && primaryButtonText != null)
                    const SizedBox(width: 16),
                  if (primaryButtonText != null)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: onPrimaryPressed,
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              primaryButtonColor ?? AppColors.viewColor,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        child: OpenSansText(
                          primaryButtonText!,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.whiteColor,
                        ),
                      ),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
