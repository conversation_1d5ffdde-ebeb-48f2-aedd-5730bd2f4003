import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { wrapRedWoodUIParams } from 'packages/http/redWoodUIWrapper';
import { DocumentDeliveryPreferencesV2Model } from '../models/document/documentDeliveryPreferencesV2.model';
import { DocumentDeliveryPreferencesV2ItemPostRequestModel } from '../models/document/documentDeliveryPreferencesV2ItemPostRequest.model';
import { DocumentDeliveryPreferencesV2ItemResponseModel } from '../models/document/documentDeliveryPreferencesV2ItemResponse.model';
import { DocumentRecordsModel } from '../models/document/documentRecords.model';
import { DocumentRecordsAttachmentsAttachmentsPreviewItemResponseModel } from '../models/document/documentRecordsAttachmentsAttachmentsPreviewItemResponse.model';
import { DocumentRecordsAttachmentsItemPostRequestModel } from '../models/document/documentRecordsAttachmentsItemPostRequest.model';
import { DocumentRecordsAttachmentsItemResponseModel } from '../models/document/documentRecordsAttachmentsItemResponse.model';
import { DocumentRecordsAttachmentsPreviewItemResponseModel } from '../models/document/documentRecordsAttachmentsPreviewItemResponse.model';
import { DocumentRecordsBannerOverrideMessagesItemResponseModel } from '../models/document/documentRecordsBannerOverrideMessagesItemResponse.model';
import { DocumentRecordsDocumentRecordsDDFItemPostRequestModel } from '../models/document/documentRecordsDocumentRecordsDDFItemPostRequest.model';
import { DocumentRecordsDocumentRecordsDDFItemResponseModel } from '../models/document/documentRecordsDocumentRecordsDDFItemResponse.model';
import { DocumentRecordsDocumentRecordsDFFItemPostRequestModel } from '../models/document/documentRecordsDocumentRecordsDFFItemPostRequest.model';
import { DocumentRecordsDocumentRecordsDFFItemResponseModel } from '../models/document/documentRecordsDocumentRecordsDFFItemResponse.model';
import { DocumentRecordsHrDocumentTypesDocumentTypesDDFItemResponseModel } from '../models/document/documentRecordsHrDocumentTypesDocumentTypesDDFItemResponse.model';
import { DocumentRecordsHrDocumentTypesDocumentTypesDFFItemResponseModel } from '../models/document/documentRecordsHrDocumentTypesDocumentTypesDFFItemResponse.model';
import { DocumentRecordsHrDocumentTypesItemResponseModel } from '../models/document/documentRecordsHrDocumentTypesItemResponse.model';
import { DocumentRecordsItemPostRequestModel } from '../models/document/documentRecordsItemPostRequest.model';
import { DocumentRecordsItemResponseModel } from '../models/document/documentRecordsItemResponse.model';
import { HrDocumentTypesLOVModel } from '../models/hr/hrDocumentTypesLOV.model';
@Injectable()
export class DocumentService {
  constructor(private readonly http: HttpService) {}

  public async getDocumentRecordsDFF(
    documentsOfRecordId: number,
    documentsOfRecordId3: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsDocumentRecordsDFFItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsDocumentRecordsDFFItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/documentRecordsDFF/${documentsOfRecordId3}`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          DocumentsOfRecordId3: documentsOfRecordId3,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async checkViewUnpublishedAccess(
    model: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemResponseModel>(
      `documentRecords/action/checkViewUnpublishedAccess`,
      model,
    );
    return response;
  }

  public async checkManageAccess(
    model: DocumentRecordsHrDocumentTypesItemResponseModel,
  ): Promise<DocumentRecordsHrDocumentTypesItemResponseModel> {
    // -- not used
    const response =
      await this.http.post<DocumentRecordsHrDocumentTypesItemResponseModel>(
        `documentRecords/{DocumentsOfRecordId}/child/hrDocumentTypes/action/checkManageAccess`,
        model,
      );
    return response;
  }

  public async getDocumentRecordsDDF(
    documentsOfRecordId: number,
    documentsOfRecordId2: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsDocumentRecordsDDFItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsDocumentRecordsDDFItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/documentRecordsDDF/${documentsOfRecordId2}`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          DocumentsOfRecordId2: documentsOfRecordId2,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async getDocumentRecordsAttachmentsPreview(
    documentsOfRecordId: number,
    attachmentsUniqID: string,
    attachmentsPreviewUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsAttachmentsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsAttachmentsAttachmentsPreviewItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/attachments/${attachmentsUniqID}/child/AttachmentsPreview/${attachmentsPreviewUniqID}`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          attachmentsUniqID: attachmentsUniqID,
          AttachmentsPreviewUniqID: attachmentsPreviewUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async getAttachmentsPreviewFileContent(
    documentsOfRecordId: number,
    attachmentsPreviewUniqID: string,
  ): Promise<DocumentRecordsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsAttachmentsPreviewItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/attachmentsPreview/${attachmentsPreviewUniqID}/enclosure/FileContents`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          attachmentsPreviewUniqID: attachmentsPreviewUniqID,
        },
        false,
      );
    return response;
  }

  public async putAttachmentsPreviewFileContent(
    model: DocumentRecordsAttachmentsPreviewItemResponseModel,
  ): Promise<DocumentRecordsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const response =
      await this.http.put<DocumentRecordsAttachmentsPreviewItemResponseModel>(
        `documentRecords/{DocumentsOfRecordId}/child/attachmentsPreview/{attachmentsPreviewUniqID}/enclosure/FileContents`,
        model,
      );
    return response;
  }

  public async checkPersonDocumentTypeCreateAccess(
    model: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemResponseModel>(
      `documentRecords/action/checkPersonDocumentTypeCreateAccess`,
      model,
    );
    return response;
  }

  public async postDocumentRecordsDFFList(
    model: DocumentRecordsDocumentRecordsDFFItemPostRequestModel,
  ): Promise<DocumentRecordsDocumentRecordsDFFItemPostRequestModel> {
    // -- not used
    const response =
      await this.http.post<DocumentRecordsDocumentRecordsDFFItemPostRequestModel>(
        `documentRecords/{DocumentsOfRecordId}/child/documentRecordsDFF`,
        model,
      );
    return response;
  }

  public async isApprovalInProgress(
    model: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemResponseModel>(
      `documentRecords/action/isApprovalInProgress`,
      model,
    );
    return response;
  }

  public async postDocumentRecordsDDFList(
    model: DocumentRecordsDocumentRecordsDDFItemPostRequestModel,
  ): Promise<DocumentRecordsDocumentRecordsDDFItemPostRequestModel> {
    // -- not used
    const response =
      await this.http.post<DocumentRecordsDocumentRecordsDDFItemPostRequestModel>(
        `documentRecords/{DocumentsOfRecordId}/child/documentRecordsDDF`,
        model,
      );
    return response;
  }

  public async getDocumentRecords_2(
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    links?: string,
    limit?: number,
    offset?: number,
    totalResults?: boolean,
    q?: string,
    orderBy?: string,
    finder?: string,
  ): Promise<DocumentRecordsModel> {
    // -- not used
    const response = await this.http.get<DocumentRecordsModel>(
      `documentRecords`,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        links: links,
        limit: limit,
        offset: offset,
        totalResults: totalResults,
        q: q,
        orderBy: orderBy,
        finder: finder,
        // where: whereClause,
      },
      true,
    );
    return response;
  } // .....................

  public async getDocumentHrRecords(
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    links?: string,
    limit?: number,
    offset?: number,
    totalResults?: boolean,
    q?: string,
    orderBy?: string,
    finder?: string,
  ): Promise<HrDocumentTypesLOVModel> {
    // -- not used
    const response = await this.http.get<HrDocumentTypesLOVModel>(
      `/hrDocumentTypesLOV`,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        links: links,
        limit: limit,
        offset: offset,
        totalResults: totalResults,
        q: q,
        orderBy: orderBy,
        finder: finder,
      },
      true,
    );
    return response;
  }
  // ..........................................

  public async postDocumentRecords_2(
    model: DocumentRecordsItemPostRequestModel,
  ): Promise<DocumentRecordsItemPostRequestModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemPostRequestModel>(
      ``,
      wrapRedWoodUIParams({
        contextPath: 'businessProcessTransactionContextInformation',
        apiPath: 'documentRecords',
        contextModuleIdentifier: 'DocApproval',
        submitPath: 'businessProcessTransactionContextInformation',
        submitOperationType: 'SubmitForApproval',
        payload: model,
      }),{
      headers: {
        'Content-Type': 'application/vnd.oracle.adf.batch+json',
      },
    }

    );
    return response;
  }

  public async getDocumentTypesDDF(
    documentsOfRecordId: number,
    documentTypeId: number,
    documentTypeId2: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsHrDocumentTypesDocumentTypesDDFItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsHrDocumentTypesDocumentTypesDDFItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/hrDocumentTypes/${documentTypeId}/child/documentTypesDDF/${documentTypeId2}`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          DocumentTypeId: documentTypeId,
          DocumentTypeId2: documentTypeId2,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async getAttachmentsPreviewFileWebImage(
    documentsOfRecordId: number,
    attachmentsPreviewUniqID: string,
  ): Promise<DocumentRecordsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsAttachmentsPreviewItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/attachmentsPreview/${attachmentsPreviewUniqID}/enclosure/FileWebImage`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          attachmentsPreviewUniqID: attachmentsPreviewUniqID,
        },
        false,
      );
    return response;
  }

  public async downloadAttachments(
    model: DocumentRecordsAttachmentsItemResponseModel,
  ): Promise<DocumentRecordsAttachmentsItemResponseModel> {
    // -- not used
    const response =
      await this.http.post<DocumentRecordsAttachmentsItemResponseModel>(
        `documentRecords/action/downloadAttachments`,
        model,
      );
    return response;
  }

  public async checkManageAccess_2(
    model: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemResponseModel>(
      `documentRecords/action/checkManageAccess`,
      model,
    );
    return response;
  }

  public async checkMassdownloadAccess(
    model: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemResponseModel>(
      `documentRecords/action/checkMassdownloadAccess`,
      model,
    );
    return response;
  }

  public async checkPersonDocumentTypeEditAccess(
    model: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemResponseModel>(
      `documentRecords/action/checkPersonDocumentTypeEditAccess`,
      model,
    );
    return response;
  }

  public async getHRDocumentTypes(
    documentsOfRecordId: number,
    documentTypeId: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsHrDocumentTypesItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsHrDocumentTypesItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/hrDocumentTypes/${documentTypeId}`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          DocumentTypeId: documentTypeId,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async getAttachmentsPreviewById(
    documentsOfRecordId: number,
    attachmentsPreviewUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsAttachmentsPreviewItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsAttachmentsPreviewItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/attachmentsPreview/${attachmentsPreviewUniqID}`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          attachmentsPreviewUniqID: attachmentsPreviewUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async postDocumentAttachments(
    model: DocumentRecordsAttachmentsItemPostRequestModel,
  ): Promise<DocumentRecordsAttachmentsItemPostRequestModel> {
    // -- not used
    const response =
      await this.http.post<DocumentRecordsAttachmentsItemPostRequestModel>(
        `documentRecords/{DocumentsOfRecordId}/child/attachments`,
        model,
      );
    return response;
  }

 public async getDocumentAttachments(
  documentsOfRecordId: string,
): Promise<DocumentRecordsAttachmentsItemPostRequestModel> {
  const response = await this.http.get<DocumentRecordsAttachmentsItemPostRequestModel>(
    `documentRecords/${documentsOfRecordId}/child/attachments`,
  );
  return response;
}


  public async getAttachmentFileWebImage(
    documentsOfRecordId: number,
    attachmentsUniqID: string,
  ): Promise<DocumentRecordsAttachmentsItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsAttachmentsItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/attachments/${attachmentsUniqID}/enclosure/FileWebImage`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          attachmentsUniqID: attachmentsUniqID,
        },
        false,
      );
    return response;
  }

  public async findByAdvancedSearchQuery(
    model: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemResponseModel>(
      `documentRecords/action/findByAdvancedSearchQuery`,
      model,
    );
    return response;
  }

  public async getAttachmentsById(
    documentsOfRecordId: number,
    attachmentsUniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsAttachmentsItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsAttachmentsItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/attachments/${attachmentsUniqID}`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          attachmentsUniqID: attachmentsUniqID,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async generateDraftLetter(model: any): Promise<{
    result: string;
  }> {
    // -- not used
    const response = await this.http.post<{
      result: string;
    }>(`documentRecords/action/generateDraftLetter`, model, {
      headers: {
        'Content-Type': 'application/vnd.oracle.adf.action+json',
      },
    });
    return response;
  }

  public async getDocumentTypesDFF(
    documentsOfRecordId: number,
    documentTypeId: number,
    documentTypeId3: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsHrDocumentTypesDocumentTypesDFFItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsHrDocumentTypesDocumentTypesDFFItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/hrDocumentTypes/${documentTypeId}/child/documentTypesDFF/${documentTypeId3}`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          DocumentTypeId: documentTypeId,
          DocumentTypeId3: documentTypeId3,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async checkManageDocumentTypeAccess(
    model: DocumentRecordsHrDocumentTypesItemResponseModel,
  ): Promise<DocumentRecordsHrDocumentTypesItemResponseModel> {
    // -- not used
    const response =
      await this.http.post<DocumentRecordsHrDocumentTypesItemResponseModel>(
        `documentRecords/{DocumentsOfRecordId}/child/hrDocumentTypes/action/checkManageDocumentTypeAccess`,
        model,
      );
    return response;
  }

  public async getDocumentRecords(
    documentsOfRecordId: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.get<DocumentRecordsItemResponseModel>(
      `documentRecords/${documentsOfRecordId}`,
      {
        DocumentsOfRecordId: documentsOfRecordId,
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
      },
      false,
    );
    return response;
  }

  public async checkPersonDocumentTypeManageAccess(
    model: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemResponseModel>(
      `documentRecords/action/checkPersonDocumentTypeManageAccess`,
      model,
    );
    return response;
  }

  public async getBannerOverrideMessagesById(
    documentsOfRecordId: number,
    objectId: number,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
  ): Promise<DocumentRecordsBannerOverrideMessagesItemResponseModel> {
    // -- not used
    const response =
      await this.http.get<DocumentRecordsBannerOverrideMessagesItemResponseModel>(
        `documentRecords/${documentsOfRecordId}/child/bannerOverrideMessages/${objectId}`,
        {
          DocumentsOfRecordId: documentsOfRecordId,
          ObjectId: objectId,
          expand: expand,
          fields: fields,
          onlyData: onlyData,
          dependency: dependency,
          links: links,
        },
        false,
      );
    return response;
  }

  public async checkPersonDocumentTypeDeleteAccess(
    model: DocumentRecordsItemResponseModel,
  ): Promise<DocumentRecordsItemResponseModel> {
    // -- not used
    const response = await this.http.post<DocumentRecordsItemResponseModel>(
      `documentRecords/action/checkPersonDocumentTypeDeleteAccess`,
      model,
    );
    return response;
  }

  public async getFileContents(
    documentsOfRecordId: number,
    attachmentsUniqID: string,
  ): Promise<Buffer> {
    // -- not used
    const response =
      await this.http.get<ArrayBuffer>(
        `documentRecords/${documentsOfRecordId}/child/attachments/${attachmentsUniqID}/enclosure/FileContents`,
        // {
        //   DocumentsOfRecordId: documentsOfRecordId,
        //   AttachedDocumentId: attachmentsUniqID,
        // },
        {},
     false,
        { baseURL: 'https://fa-evxq-test-saasfaprod1.fa.ocs.oraclecloud.com:443/hcmRestApi/resources/11.13.18.05',
          responseType: 'arraybuffer',
         }
      );
    return Buffer.from(response);;
  }

  public async putFileContents(
    model: DocumentRecordsAttachmentsItemResponseModel,
  ): Promise<DocumentRecordsAttachmentsItemResponseModel> {
    // -- not used
    const response =
      await this.http.put<DocumentRecordsAttachmentsItemResponseModel>(
        `documentRecords/{DocumentsOfRecordId}/child/attachments/{attachmentsUniqID}/enclosure/FileContents`,
        model,
      );
    return response;
  }

  public async getPreferences(
    documentDeliveryPreferencesV2UniqID: string,
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    dependency?: string,
    links?: string,
    effectiveOf?: string,
  ): Promise<DocumentDeliveryPreferencesV2ItemResponseModel[]> {
    // -- not used
    const response = await this.http.get<
      DocumentDeliveryPreferencesV2ItemResponseModel[]
    >(
      `documentDeliveryPreferencesV2/${documentDeliveryPreferencesV2UniqID}`,
      {
        documentDeliveryPreferencesV2UniqID:
          documentDeliveryPreferencesV2UniqID,
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        dependency: dependency,
        links: links,
        'Effective-Of': effectiveOf,
      },
      true,
    );
    return response;
  }

  public async getDocumentPreferences(
    expand?: string,
    fields?: string,
    onlyData?: boolean,
    links?: string,
    limit?: number,
    offset?: number,
    totalResults?: boolean,
    q?: string,
    orderBy?: string,
    finder?: string,
    effectiveDate?: string,
    effectiveOf?: string,
  ): Promise<DocumentDeliveryPreferencesV2Model[]> {
    // -- not used
    const response = await this.http.get<DocumentDeliveryPreferencesV2Model[]>(
      `documentDeliveryPreferencesV2`,
      {
        expand: expand,
        fields: fields,
        onlyData: onlyData,
        links: links,
        limit: limit,
        offset: offset,
        totalResults: totalResults,
        q: q,
        orderBy: orderBy,
        finder: finder,
        effectiveDate: effectiveDate,
        'Effective-Of': effectiveOf,
      },
      true,
    );
    return response;
  }

  public async postDocumentPreferences(
    model: DocumentDeliveryPreferencesV2ItemPostRequestModel,
  ): Promise<DocumentDeliveryPreferencesV2ItemPostRequestModel> {
    // -- not used
    const response =
      await this.http.post<DocumentDeliveryPreferencesV2ItemPostRequestModel>(
        `documentDeliveryPreferencesV2`,
        model,
      );
    return response;
  }
}
