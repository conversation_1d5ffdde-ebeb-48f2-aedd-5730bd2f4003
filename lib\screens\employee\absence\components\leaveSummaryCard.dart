import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/absence/absenceSelector.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/utils/style/sizeConfig.dart';
import 'package:seawork/utils/util.dart';

class LeaveSummaryCard extends ConsumerStatefulWidget {
  final List<Map<String, dynamic>> leaveTypes;
  final bool isLoading;
  final List<String> leaveTypeNames;
  final double totalLeaves;
  final double leaveBalance;
  final double leaveTaken;
  final Function(Map<String, dynamic>) selectLeaveType;
  final Future<void> Function(
    BuildContext context,
    WidgetRef ref,
    String fileName,
  )
  generateAndSavePDF;
  final void Function(bool) onUpdateLeaveTypeFlag;

  const LeaveSummaryCard({
    Key? key,
    required this.leaveTypes,
    required this.isLoading,
    required this.onUpdateLeaveTypeFlag,
    required this.leaveTypeNames,
    required this.totalLeaves,
    required this.leaveBalance,
    required this.leaveTaken,
    required this.selectLeaveType,
    required this.generateAndSavePDF,
  }) : super(key: key);

  @override
  ConsumerState<LeaveSummaryCard> createState() => _LeaveSummaryCardState();
}

class _LeaveSummaryCardState extends ConsumerState<LeaveSummaryCard> {
  String? _selectedLeaveType;
  bool isDownloading = false;

  @override
  void initState() {
    super.initState();
    // Initialize selectedLeaveType with the first leave type if available
    if (widget.leaveTypes.isNotEmpty) {
      _selectedLeaveType = widget.leaveTypes.first['name'];
    }
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig.init(context);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 5, vertical: 5),
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowBoxColor.withOpacity(
              0.25,
            ), // #80CAD640 (40 = 0.25 opacity)
            blurRadius: 9.6,
            spreadRadius: 1,
            offset: Offset.zero,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(
          top: 20,
          bottom: 20,
          left: 12,
          right: 12,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child:
                      widget.leaveTypes.isNotEmpty
                          ? AbsenceSelector(
                            selectedValue: capitalizeFirstWordOnly(
                              _selectedLeaveType ??
                                  widget.leaveTypes.first['name'],
                            ),
                            onChanged: (newValue) {
                              setState(() {
                                // Update selected leave type when the dropdown value changes
                                _selectedLeaveType = newValue;
                                final selectedMap = widget.leaveTypes
                                    .firstWhere(
                                      (e) =>
                                          e['name']
                                              .toString()
                                              .toLowerCase()
                                              .trim() ==
                                          newValue
                                              .toString()
                                              .toLowerCase()
                                              .trim(),
                                      orElse: () => {},
                                    );
                                if (selectedMap.isNotEmpty) {
                                  widget.selectLeaveType(selectedMap);
                                  widget.onUpdateLeaveTypeFlag(true);
                                }
                              });
                            },
                            hintText: "Annual leave",
                            hintStyle: const TextStyle(
                              fontSize: 14,
                              color: AppColors.viewColor,
                            ),
                            decoration: InputDecoration(
                              fillColor: AppColors.whiteColor,
                              filled: true,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 12,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: AppColors.lightGreyColor2,
                                  width: 1.w,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: AppColors.lightGreyColor2,
                                  width: 1.w,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: AppColors.lightGreyColor2,
                                  width: 2.w,
                                ),
                              ),
                            ),
                            isLoading: widget.isLoading,
                            options: widget.leaveTypeNames,
                          )
                          : const Center(
                            child: Text("No leave types available"),
                          ),
                ),
                // const SizedBox(width: 8),
                // isDownloading
                //     ? const Padding(
                //       padding: EdgeInsets.all(8.0),
                //       child: CustomLoadingWidget(),
                //     )
                //     : GestureDetector(
                //       onTap: () async {
                //         setState(() {
                //           isDownloading = true;
                //         });
                //         try {
                //           await widget.generateAndSavePDF(
                //             context,
                //             ref,
                //             "Leave_Summary",
                //           );
                //         } catch (e) {
                //         } finally {
                //           setState(() {
                //             isDownloading = false;
                //           });
                //         }
                //       },
                //       child: Padding(
                //         padding: const EdgeInsets.only(left: 10.0),
                //         child: CustomSvgImage(imageName: "ic_download"),
                //       ),
                //     ),
              ],
            ),
            SizedBox(height: 20.h),
            Row(
              children: [
                // First box
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.whiteColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.viewColor,
                        width: 1.w,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(4),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          DmSansText(
                            widget.totalLeaves.toStringAsFixed(2),
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            color: AppColors.viewColor,
                          ),
                          const SizedBox(height: 8),
                          OpenSansText(
                            'Total leaves',
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.black,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 15.w),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.whiteColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColors.goldenColor,
                        width: 1.w,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                        left: 1,
                        right: 1,
                        top: 4,
                        bottom: 4,
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          DmSansText(
                            widget.leaveBalance.toStringAsFixed(2),
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color: AppColors.goldenColor,
                          ),
                          SizedBox(height: 8.h),
                          OpenSansText(
                            'Leave balance',
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: AppColors.blackColor,
                            textAlign: TextAlign.center, // Add this line
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 15.w),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.whiteColor,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.seaGreen, width: 1),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(4),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          DmSansText(
                            widget.leaveTaken.abs().toStringAsFixed(2),
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            color: AppColors.seaGreen,
                          ),
                          SizedBox(height: 8.h),
                          OpenSansText(
                            'Leave taken',
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: AppColors.blackColor,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
