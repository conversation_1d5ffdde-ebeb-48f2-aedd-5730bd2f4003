import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';

class CustomDropdown extends ConsumerStatefulWidget {
  final String label;
  final List<String> options;
  final String? selectedValue;
  final Function(String) onChanged;
  final bool showError;
  final String hintText;
  final bool hasAsterisk;

  const CustomDropdown({
    Key? key,
    required this.label,
    required this.options,
    required this.selectedValue,
    required this.onChanged,
    this.showError = false,
    this.hintText = "Select",
    this.hasAsterisk = false,
  }) : super(key: key);

  @override
  _CustomDropdownState createState() => _CustomDropdownState();
}

class _CustomDropdownState extends ConsumerState<CustomDropdown> {
  bool _isOpen = false;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  static final headingStyle = GoogleFonts.dmSans(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: Colors.black,
  );

  Widget buildHeading(String text, {bool hasAsterisk = false}) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(text: text, style: headingStyle),
          if (hasAsterisk)
            const TextSpan(
              text: ' *',
              style: TextStyle(
                color: AppColors.red,
                fontFamily: 'DMSans',
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
        ],
      ),
    );
  }

  void _toggleDropdown() {
    if (_isOpen) {
      _overlayEntry?.remove();
      _overlayEntry = null;
    } else {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
    }
    setState(() {
      _isOpen = !_isOpen;
    });
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    Size size = renderBox.size;
    Offset offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder:
          (context) => GestureDetector(
            onTap: () {
              _toggleDropdown();
            },
            behavior: HitTestBehavior.opaque,
            child: Stack(
              children: [
                Positioned(
                  width: size.width,
                  left: offset.dx,
                  top: offset.dy + size.height + 4,
                  child: Material(
                    elevation: 0,
                    color: AppColors.whiteColor,
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AppColors.lightGreyColor2,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children:
                            widget.options.map((String option) {
                              return InkWell(
                                onTap: () {
                                  widget.onChanged(option);
                                  _toggleDropdown();
                                },
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 4,
                                    vertical: 4,
                                  ),
                                  child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.only(
                                      left: 20,
                                      right: 20,
                                      top: 16,
                                      bottom: 16,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          option == widget.selectedValue
                                              ? AppColors.lightGreyColor2
                                              : AppColors.transparentColor,
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Center(
                                      child: OpenSansText(
                                        option,
                                        fontSize: 14,
                                        fontWeight: option == widget.selectedValue 
                                        ? FontWeight.w600 : FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  @override
  void dispose() {
    _overlayEntry?.remove();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildHeading(widget.label, hasAsterisk: widget.hasAsterisk),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: _toggleDropdown,
            child: Container(
              width: double.infinity,
              height: 43,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              alignment: Alignment.centerLeft,
              decoration: BoxDecoration(
                color: AppColors.whiteColor,
                border: Border.all(
                  color:
                      widget.showError
                          ? AppColors.red
                          : AppColors.lightGreyColor2,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: OpenSansText(
                      widget.selectedValue ?? widget.hintText,
                      fontSize: 14,
                      color:
                          widget.selectedValue == null
                              ? AppColors.grey
                              : AppColors.blackColor,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Transform.rotate(
                    angle: _isOpen ? 3.14159 : 0,
                    child: CustomSvgImage(imageName: "ArrowLeft"),
                  ),
                ],
              ),
            ),
          ),
          if (widget.showError)
            const Padding(
              padding: EdgeInsets.only(top: 4),
              child: Text(
                "This field is required",
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.red,
                  fontFamily: 'DMSans',
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class DropdownField extends StatelessWidget {
  final String? value;
  final List<String> items;
  final ValueChanged<String?> onChanged;
  final String hintText;

  const DropdownField({
    Key? key,
    required this.value,
    required this.items,
    required this.onChanged,
    this.hintText = "Select an option",
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 43,
      width: double.infinity,
      child: DropdownButtonFormField<String>(
        value: value,
        isExpanded: true,
        decoration: InputDecoration(
          fillColor: AppColors.whiteColor,
          filled: true,
          hintText: hintText,
          hintStyle: GoogleFonts.openSans(color: AppColors.blackColor),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppColors.lightGreyColor2,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppColors.lightGreyColor2,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppColors.lightGreyColor2,
              width: 2,
            ),
          ),
        ),
        icon: const Icon(Icons.keyboard_arrow_down, size: 24),
        items:
            items
                .map(
                  (e) => DropdownMenuItem(
                    value: e,
                    child: SizedBox(
                      width: double.infinity,
                      child: OpenSansText(
                        e,
                        overflow: TextOverflow.visible,
                        softWrap: true,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                )
                .toList(),
        onChanged: onChanged,
      ),
    );
  }
}
