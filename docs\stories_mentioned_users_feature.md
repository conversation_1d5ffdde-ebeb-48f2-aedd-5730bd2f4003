# Stories Mentioned Users Feature

## Overview
This feature displays mentioned users and associated entities next to the role text in story cards. It shows different information based on the user type who created the post.

## Implementation Details

### Location
- **File**: `lib/screens/parent/stories/storiesList.dart`
- **Method**: `_buildMentionedUsers()` in `_StoryCardWidgetState` class
- **UI Location**: Next to the role text in each story card

### Logic Flow

The feature follows this priority order:

1. **Assigned Nurseries** (for admin/staff users)
   - If `CreatedBy.AssignedNurseries` exists and is not empty
   - Shows first nursery name
   - Adds "..." if multiple nurseries exist

2. **Students** (for parent users)
   - If no assigned nurseries but `CreatedBy.Students` exists
   - Shows first student's `FullNameInEnglish`
   - Adds "..." if multiple students exist
   - Filters out empty names

3. **Empty String** (fallback)
   - If neither nurseries nor students exist

### Data Structure

#### For Admin/Staff Users (with AssignedNurseries):
```json
{
  "CreatedBy": {
    "Role": "Super Admin",
    "AssignedNurseries": [
      {
        "Id": 1104,
        "Name": "Al Falah Nursery",
        "NameInArabic": "حضانة الفلاح"
      }
    ]
  }
}
```

#### For Parent Users (with Students):
```json
{
  "CreatedBy": {
    "Role": "Father",
    "Students": [
      {
        "Id": 181,
        "FullNameInEnglish": "Niyaz",
        "FullNameInArabic": "ادم ساجد ساجد فالابيل بيديكايل"
      }
    ]
  }
}
```

### Display Examples

1. **Single Nursery**: `Super Admin • Al Falah Nursery`
2. **Multiple Nurseries**: `Super Admin • Al Falah Nursery...`
3. **Single Student**: `Father • Niyaz`
4. **Multiple Students**: `Father • Niyaz...`
5. **No Data**: `Teacher` (just the role)

### UI Implementation

The mentioned users text is displayed in a `Row` widget next to the role text:

```dart
Row(
  children: [
    OpenSans400Large(10, story.createdBy?.role ?? '', Color(0xFF7B7B7B)),
    SizedBox(width: 4),
    Expanded(
      child: OpenSans400Large(
        10,
        _buildMentionedUsers(story),
        Color(0xFF7B7B7B),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    ),
  ],
)
```

### Key Features

- **Responsive Design**: Uses `Expanded` widget to handle long names
- **Text Overflow**: Ellipsis handling for very long names
- **Priority Logic**: Nurseries take precedence over students
- **Empty Name Filtering**: Ignores students with empty names
- **Consistent Styling**: Matches the role text styling

### Testing

Comprehensive unit tests cover:
- Null safety (when `createdBy` is null)
- Single vs multiple nurseries
- Single vs multiple students
- Priority handling (nurseries over students)
- Empty name filtering
- Edge cases

Run tests with:
```bash
flutter test test/stories_mentioned_users_test.dart
```

## Technical Concepts Used

### Flutter Widgets
- **Row**: Horizontal layout for role and mentioned users
- **Expanded**: Responsive width allocation
- **SizedBox**: Spacing between elements

### Dart Programming
- **Null Safety**: Safe navigation with `?.` operator
- **List Operations**: `map()`, `where()`, `isNotEmpty`
- **String Interpolation**: Building display strings
- **Conditional Logic**: Priority-based display logic

### Provider Pattern
- **State Management**: Uses existing `StoriesProvider`
- **Data Access**: Accesses story data through provider
- **Reactive UI**: Updates when story data changes

This implementation provides a clean, efficient way to display contextual user information in story cards while maintaining good performance and user experience.

## Tappable Mentioned Users Feature (Updated)

### New Components Added

#### 1. MentionedUser Model (`lib/models/mentioned_user.dart`)
```dart
enum MentionUserType { student, nursery }

class MentionedUser {
  final String name;
  final MentionUserType type;
  MentionedUser({required this.name, required this.type});
}
```

#### 2. Bottom Sheet Component (`lib/screens/parent/stories/components/mention_users_and_nurseries.dart`)
- **Responsive design**: No Positioned/Stack layouts
- **Device compatibility**: Uses constraints and flexible layouts
- **Icon switching**: Automatically shows `students_stories.svg` or `nurseries_stories.svg`
- **Scrollable list**: Handles any number of mentioned users

### Updated Functionality

#### Tappable Mentioned Users
The mentioned users text is now tappable and shows a bottom sheet with:
- **Complete list** of all mentioned users/nurseries
- **Appropriate icons** for each type (student vs nursery)
- **Responsive layout** that works on all device sizes
- **Smooth animations** with modal bottom sheet

#### UI Flow
1. **Story Card Display**: Shows abbreviated mentioned users (e.g., "Father • Niyaz...")
2. **Tap Interaction**: User taps on the mentioned users text
3. **Bottom Sheet**: Opens with complete list of all mentioned users
4. **Visual Distinction**: Each row shows appropriate icon and name

### Technical Implementation

#### Helper Methods Added
```dart
// Shows the bottom sheet
Future<void> _showMentionedBottomSheet(BuildContext context, List<MentionedUser> users)

// Generates list of MentionedUser objects
List<MentionedUser> _getMentionedUsersList(StoriesListModel story)

// Updated to return tappable Widget instead of String
Widget _buildMentionedUsers(StoriesListModel story)
```

#### Bottom Sheet Features
- **Drag handle**: Visual indicator for dismissal
- **Title**: "Mentioned Users" header
- **Scrollable list**: Handles long lists efficiently
- **Individual rows**: 64px height with proper spacing
- **Icon display**: SVG icons for visual distinction
- **Text overflow**: Ellipsis for long names

### Assets Required
- `assets/images/students_stories.svg` ✅ (exists)
- `assets/images/nurseries_stories.svg` ✅ (exists)

### Testing Coverage
- **13 comprehensive tests** covering:
  - MentionedUser model creation
  - List generation for different user types
  - Empty name filtering
  - Priority handling
  - Edge cases and null safety

### User Experience Improvements
- **Progressive disclosure**: Shows summary first, details on demand
- **Visual feedback**: Clear icons distinguish user types
- **Accessibility**: Proper touch targets and readable text
- **Performance**: Efficient list rendering with separation builders

This enhanced implementation maintains the original display functionality while adding rich interaction capabilities through the responsive bottom sheet interface.
