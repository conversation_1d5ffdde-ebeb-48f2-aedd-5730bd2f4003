
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:seawork/data/module/networkModule.dart';
import 'package:seawork/screens/account/profile/models/userProfileModel.dart';

class UserProfileRepository {
  final Dio _dio;

  UserProfileRepository(this._dio);

  static Dio createDio() {
    Dio dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 10), // Set connection timeout
      receiveTimeout: const Duration(seconds: 10), // Set receive timeout
    ));

    if (NetworkModule.currentEnvironment == Environment.dev && !kIsWeb) {
      dio.httpClientAdapter = IOHttpClientAdapter(
        createHttpClient: () {
          final client = HttpClient();
          client.badCertificateCallback =
              (X509Certificate cert, String host, int port) => true;
          client.connectionTimeout = const Duration(seconds: 10); // Additional timeout
          return client;
        },
      );
    }

    return dio;
  }

  Future<UserProfileModel?> getUserProfileDetails(String personId) async {
    personId = "***************";
    try {
      final response = await _dio.get(
        '${NetworkModule.baseUrlEMS}/auth/user-profile/$personId',
        options: Options(
          receiveTimeout: const Duration(seconds: 10), // Per-request timeout
        ),
      );

      if (kDebugMode) {
        print('Response Data Type: ${response.data.runtimeType}');
        print('Response Data: ${response.data}');
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is Map<String, dynamic>) {
          return UserProfileModel.fromJson(response.data);
        } else {
          if (kDebugMode) {
            print('Unexpected data format: ${response.data}');
          }
          throw Exception('Unexpected response format');
        }
      } else {
        if (kDebugMode) {
          print('Failed to fetch employee details: ${response.statusCode}');
        }
        throw Exception('Failed to fetch employee details');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        if (kDebugMode) {
          print('Request timed out: $e');
        }
      } else {
        if (kDebugMode) {
          print('Error fetching employee details: $e');
        }
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected error: $e');
      }
      return null;
    }
  }
}