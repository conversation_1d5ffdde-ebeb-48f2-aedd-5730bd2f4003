import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/bottomSheetProvider/bottomSheetProvider.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/employee/claim/models/claimDummyModel.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/absence/leaveTypesCustomSheets.dart';

class ClaimTabBarWidget extends ConsumerStatefulWidget {
  final String status;
  final int? selectedTabIndex;
  final String? iconClicked;

  const ClaimTabBarWidget({
    Key? key,
    required this.status,
    this.selectedTabIndex,
    this.iconClicked,
  }) : super(key: key);

  @override
  ConsumerState<ClaimTabBarWidget> createState() => _ClaimTabBarWidgetState();
}

class _ClaimTabBarWidgetState extends ConsumerState<ClaimTabBarWidget> {
  final Map<int, bool> _isPressedMap = {};

  @override
  Widget build(BuildContext context) {
    List<Claim> claims = Claim.getDummyClaims();
    String Progress = '';
    Color ProgressColor = AppColors.goldenYellow;
    if (widget.status == 'pending') {
      Progress = 'In progress';
    } else if (widget.status == 'approved') {
      Progress = 'Approved';
      ProgressColor = AppColors.seaGreen;
    } else if (widget.status == 'rejected') {
      Progress = 'Rejected';
      ProgressColor = AppColors.Orange;
    } else {
      Progress = 'Withdrawn';
      ProgressColor = AppColors.softPurple;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ListView.builder(
            itemCount: claims.length,
            itemBuilder: (context, index) {
              Claim claim = claims[index];

              return GestureDetector(
                onTapDown: (_) {
                  setState(() {
                    _isPressedMap[index] = true;
                  });
                },
                onTapUp: (_) {
                  setState(() {
                    _isPressedMap[index] = false;
                  });
                },
                onTapCancel: () {
                  setState(() {
                    _isPressedMap[index] = false;
                  });
                },
                onTap: () {
                  ref.read(bottomSheetVisibilityProvider.notifier).state = true;
                  showModalBottomSheet(
                    backgroundColor: AppColors.transparentColor,
                    context: context,
                    isScrollControlled: true,
                    builder:
                        (context) => CustomBottomSheet(
                          assigntomePending:
                              (widget.iconClicked == 'Apply leave' &&
                                  widget.selectedTabIndex == 1),
                          status: widget.status,
                          iconClicked: widget.iconClicked,
                          SelectedTabIndex: widget.selectedTabIndex,
                        ),
                  );
                },
                child: IntrinsicHeight(
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 353,
                    margin: const EdgeInsets.only(
                      left: 12,
                      right: 12,
                      bottom: 12,
                    ),
                    decoration: BoxDecoration(
                      color:
                          _isPressedMap[index] == true
                              ? AppColors.microinteraction
                              : AppColors.whiteColor,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.boxshadowcolor.withOpacity(0.25),
                          offset: const Offset(0, 0),
                          blurRadius: 9.6,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              DmSansText(
                                claim.claimName,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.blackColor,
                                fontheight: 18.23 / 14,
                              ),
                              if (widget.iconClicked == 'Apply leave' &&
                                  widget.selectedTabIndex != 1)
                                Image.asset(
                                  "assets/images/ai.png",
                                  width: 16,
                                  height: 16,
                                ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  CustomSvgImage(
                                    imageName: "cardcalender",
                                    width: 16,
                                    height: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  OpenSansText(
                                    claim.date,
                                    // style: const TextStyle(
                                    // fontFamily: 'Open Sans',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.lightGreyColor,
                                    // ),
                                  ),
                                ],
                              ),
                              if (claim.claimName[index] != 'Time off in lieu')
                                DmSansText(
                                  '90.00 AED',
                                  // style: TextStyle(
                                  // fontFamily: 'DM Sans',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.blackColor,
                                  // ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          // const Divider(color: AppColors.lightGreyColor3),
                          // Padding(
                          //   padding: const EdgeInsets.only(top: 12),
                          //   child: Row(
                          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //     children: [
                          //       OpenSansText(
                          //         'Requested by',
                          //         // style: TextStyle(
                          //         // fontFamily: 'Open Sans',
                          //         fontSize: 12,
                          //         fontWeight: FontWeight.w600,
                          //         color: AppColors.blackColor,
                          //         // ),
                          //       ),
                          //       SizedBox(
                          //         height: 8,
                          //       ),
                          //       OpenSansText(
                          //         claim.requestedBy,
                          //         // style: const TextStyle(
                          //         // fontFamily: 'Open Sans',
                          //         fontSize: 12,
                          //         fontWeight: FontWeight.w400,
                          //         color: AppColors.lightGreyColor,
                          //         // ),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                          // SizedBox(height: 8),
                          // if (widget.selectedTabIndex != 1)
                          //   Row(
                          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //     children: [
                          //       OpenSansText(
                          //         'Status',
                          //         // style: TextStyle(
                          //         fontFamily: 'Open Sans',
                          //         fontSize: 12,
                          //         fontWeight: FontWeight.w600,
                          //         color: AppColors.blackColor,
                          //         fontheight: 18.23 / 14,
                          //         // ),
                          //       ),
                          //       Text(
                          //         Progress,
                          //         style: TextStyle(
                          //           fontFamily: 'Open Sans',
                          //           fontSize: 12,
                          //           fontWeight: FontWeight.w400,
                          //           color: ProgressColor,
                          //         ),
                          //       ),
                          //     ],
                            // ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
