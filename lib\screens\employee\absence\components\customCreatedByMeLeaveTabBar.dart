import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import 'package:seawork/components/bottomSheetProvider/bottomSheetProvider.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/dateFormatter.dart';

import 'package:seawork/screens/employee/absence/form.dart';
import 'package:seawork/screens/employee/absence/leaveTypesCustomSheets.dart';
import 'package:seawork/screens/employee/absence/models/getAbsences.dart';
import 'package:seawork/screens/employee/absence/providers/absenceListNotifier.dart';
import 'package:seawork/screens/employee/absence/providers/absencesNotifier.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/utils/util.dart';

// ignore: must_be_immutable
class CreatedByMeLeaveTabBarWidget extends ConsumerStatefulWidget {
  String status;
  final int? selectedTabIndex;
  final String? iconClicked;
  final List<AbsenceItem> absenceData;

  dynamic leaveTypes;

  CreatedByMeLeaveTabBarWidget({
    Key? key,
    required this.status,
    this.selectedTabIndex,
    this.iconClicked,
    this.leaveTypes,
    required this.absenceData,
  }) : super(key: key);
  @override
  _LeaveTabBarWidgetState createState() => _LeaveTabBarWidgetState();
}

class _LeaveTabBarWidgetState
    extends ConsumerState<CreatedByMeLeaveTabBarWidget> {
  late ScrollController _scrollController;
  bool _isLoadingMore = false;
  Timer? _debounce;

  final Map<int, bool> _isPressedMap = {};

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController()..addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(offsetProvider.notifier).state = 0;
    });
  }

  double _lastOffset = 0;

  void _onScroll() {
    final currentOffset = _scrollController.position.pixels;
    final maxExtent = _scrollController.position.maxScrollExtent;
    final viewport = _scrollController.position.viewportDimension;

    final preloadOffset = 500;
    final isNearBottom = currentOffset >= maxExtent - preloadOffset;
    final isNearTop = currentOffset <= 50;
    final canScroll = maxExtent > viewport;

    final isScrollingDown = currentOffset > _lastOffset;
    final isScrollingUp = currentOffset < _lastOffset;

    _lastOffset = currentOffset;

    if (isScrollingDown && isNearBottom && !_isLoadingMore && canScroll) {
      _loadMore(); // no debounce or delay
    }

    if (isScrollingUp && isNearTop && !_isLoadingMore && canScroll) {
      _loadPreviousData();
    }
  }

  Future<void> _loadPreviousData() async {
    if (_isLoadingMore) return;

    setState(() => _isLoadingMore = true);

    final currentOffset = ref.read(offsetProvider);
    final newOffset = currentOffset > 0 ? currentOffset - 10 : 0;

    if (newOffset == currentOffset) {
      setState(() => _isLoadingMore = false);
      return;
    }

    ref.read(offsetProvider.notifier).state = newOffset;

    switch (widget.status.toLowerCase()) {
      case "pending":
        await ref
            .read(awaitingListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset, prepend: true);
        break;
      case "approved":
        await ref
            .read(approvedListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset, prepend: true);
        break;
      case "rejected":
        await ref
            .read(deniedListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset, prepend: true);
        break;
      case "withdrawn":
        await ref
            .read(withdrawnListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset, prepend: true);
        break;
      case "draft":
        await ref
            .read(draftListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset, prepend: true);
        break;
      default:
        // Handle any default case (perhaps an error or no status)
        break;
    }

    setState(() => _isLoadingMore = false);
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;

    setState(() => _isLoadingMore = true);

    final currentOffset = ref.read(offsetProvider);
    final newOffset = currentOffset + 10;

    ref.read(offsetProvider.notifier).state = newOffset;

    switch (widget.status.toLowerCase()) {
      case "pending":
        await ref
            .read(awaitingListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset);
        break;
      case "approved":
        await ref
            .read(approvedListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset);
        break;
      case "rejected":
        await ref
            .read(deniedListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset);
        break;
      case "withdrawn":
        await ref
            .read(withdrawnListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset);
        break;
      case "draft":
        await ref
            .read(draftListNotifierProvider.notifier)
            .fetchPaginatedData(offset: newOffset);
        break;
      default:
        // Handle any default case (perhaps an error or no status)
        break;
    }

    setState(() => _isLoadingMore = false);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _scrollController.dispose();
    ref.read(taskStatusProvider.notifier).state = '';
    super.dispose();
  }

  String AppliedForDateRange(String? startDate, String? endDate) {
    if (startDate == null || endDate == null) return '';
    try {
      DateTime start = DateTime.parse(startDate).toLocal();
      DateTime end = DateTime.parse(endDate).toLocal();
      return "${DateFormat('dd-MM-yyyy').format(start)} - ${DateFormat('dd-MM-yyyy').format(end)}";
    } catch (e) {
      return '';
    }
  }

  Map<String, String> getSubValues(
      String leaveType, String formattedDate, AbsenceItem absence) {
    var duration = absence.duration?.toString() ??
        'N/A'; // Ensure duration is extracted safely

    if (widget.iconClicked == "Apply Leave" &&
        widget.selectedTabIndex == 0 &&
        widget.status != "Draft") {
      return {
        "Applied for": formattedDate,
        "Duration": '$duration ${duration == '1' ? 'Day' : 'Days'}',
      };
    } else if (widget.iconClicked == "Apply Leave" &&
        widget.selectedTabIndex == 1 &&
        widget.status != "Draft") {
      return {
        "Submitted by": "John Doe",
        "Applied for": formattedDate,
        "Duration": duration,
      };
    } else if (widget.iconClicked == "Approval") {
      switch (leaveType) {
        case "Annual leave":
          return {
            "Submitted by": "John Doe",
            "Applied for": formattedDate,
            "Duration": duration
          };
        case "Dependents insurance":
          return {
            "Dependent Name": "John's Son",
            "Insurance Type": "Health Coverage",
            "Requested by": "John Doe",
          };
        case "Salary certificate":
          return {
            "Requested by": "John Doe",
            "Issued on": formattedDate,
          };
      }
    }
    return {};
  }

  List<Map<String, dynamic>> generateAbsences() {
    return List.generate(widget.absenceData.length, (index) {
      AbsenceItem absence =
          widget.absenceData[index]; // Store object for better readability

      // Parse and format the start and end dates
      String startDate = formatDate(absence.startDate);
      String endDate = formatDate(absence.endDate);

      // Ensure the correct date format is applied
      String formattedDate = startDate.isNotEmpty && endDate.isNotEmpty
          ? "$startDate - $endDate"
          : startDate.isNotEmpty
              ? startDate
              : "No Date";

      String leaveType = absence.absenceType ?? '';

      return {
        "name": leaveType,
        "date": formattedDate,
        "amount": "\$100",
        "typeDetails": getSubValues(leaveType, formattedDate, absence),
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    List<Map<String, dynamic>> generatedAbsences = generateAbsences();

    // Do not modify the `status` variable
    String progress;
    Color progressColor = const Color(0XFFE2A900);

    switch (widget.status) {
      case 'pending':
        progress = 'In progress';
        break;
      case 'approved':
        progress = 'Approved';
        progressColor = const Color(0XFF08AA78);
        break;
      case 'rejected':
        progress = 'Rejected';
        progressColor = const Color(0XFFFF6332);
        break;
      default:
        progress = 'Withdrawn'; // Default status
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: NotificationListener<ScrollNotification>(
            onNotification: (scrollNotification) {
              if (scrollNotification is OverscrollNotification &&
                  _scrollController.position.pixels >=
                      _scrollController.position.maxScrollExtent &&
                  !_isLoadingMore) {
                _loadMore();
              }
              return false;
            },
            child: ListView.builder(
              controller: _scrollController, // Attach the scroll controller
              itemCount: widget.absenceData.length +
                  (widget.absenceData.length <= 2 ? 1 : 0),
              itemBuilder: (context, index) {
                if (widget.absenceData.length <= 2 &&
                    index == widget.absenceData.length) {
                  // return Center(
                  //   child: IconButton(
                  //     icon: const Icon(Icons.refresh_rounded,
                  //         size: 36, color: AppColors.meetingdetailsbuttoncolor),
                  //     onPressed: () async {
                  //       ref.read(offsetProvider.notifier).state = 0;
                  //       await ref
                  //           .read(awaitingListNotifierProvider.notifier)
                  //           .fetchPaginatedData(offset: 0);
                  //     },
                  //   ),
                  // );
                }
                if (index == widget.absenceData.length) {
                  // If it's the last item, show the loading indicator
                  return _isLoadingMore
                      ? Center(child: CircularProgressIndicator())
                      : SizedBox.shrink(); // No more items to load
                }

                AbsenceItem absence = widget.absenceData[index];

                return GestureDetector(
                  onTapDown: (_) {
                    setState(() {
                      _isPressedMap[index] = true;
                    });
                  },
                  onTapUp: (_) {
                    setState(() {
                      _isPressedMap[index] = false;
                    });
                  },
                  onTapCancel: () {
                    setState(() {
                      _isPressedMap[index] = false;
                    });
                  },
                  onTap: () {
                    ref.read(bottomSheetVisibilityProvider.notifier).state =
                        true;

                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) {
                        if (widget.status == 'draft') {
                          return Container(
                            height: MediaQuery.of(context).size.height * 0.93,
                            child: DraggableScrollableSheet(
                              initialChildSize: 0.6,
                              minChildSize: 0.3,
                              maxChildSize: 0.85,
                              expand: false,
                              builder:
                                  (context, scrollController) => ClipRRect(
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(20.0),
                                      topRight: Radius.circular(20.0),
                                    ),
                                    child: FormLeave(
                                      isEdit: true,
                                      isDraft:
                                          widget.status == 'draft'
                                              ? true
                                              : false,
                                      leaveTypes: widget.leaveTypes,
                                      absence: absence,
                                      leaveType: absence.absenceType ?? '',
                                    ),
                                  ),
                            ),
                          );
                        } else {
                          return CustomBottomSheet(
                            assigntomePending:
                                (widget.iconClicked == 'Apply Leave' &&
                                    widget.selectedTabIndex == 1),
                            status: widget.status,
                            leaveTypes: widget.leaveTypes,
                            absence: absence,
                            iconClicked: widget.iconClicked,
                            SelectedTabIndex: widget.selectedTabIndex,
                          );
                        }
                      },
                    );
                  },
                  child: IntrinsicHeight(
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      width: 353,
                      margin: const EdgeInsets.only(left: 4, bottom: 8),
                      decoration: BoxDecoration(
                        color:
                            _isPressedMap[index] == true
                                ? AppColors.microinteraction
                                : AppColors.whiteColor,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.shadowBoxColor.withOpacity(0.25),
                            offset: const Offset(0, 0),
                            blurRadius: 9.6,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                DmSansText(
                                  capitalizeFirstWordOnly(
                                    absence.absenceType ?? '',
                                  ),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.blackColor,
                                ),
                                // Text(
                                //   absence.absenceType ?? '',
                                //   style: const TextStyle(
                                //     fontFamily: 'DM Sans',
                                //     fontSize: 14,
                                //     fontWeight: FontWeight.w600,
                                //     color: Color(0xFF062540),
                                //   ),
                                // ),
                                if (widget.iconClicked == 'Apply Leave' &&
                                    widget.selectedTabIndex != 1 &&
                                    widget.status != 'withdrawn')
                                  GestureDetector(
                                    onTap: () {
                                      ref
                                          .read(bottomSheetVisibilityProvider
                                              .notifier)
                                          .state = true;

                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder:
                                              (context) => FormLeave(
                                                isEdit: true,
                                                isDraft:
                                                    widget.status == 'Draft',
                                                leaveTypes: widget.leaveTypes,
                                                absence: absence,
                                                leaveType:
                                                    absence.absenceType ?? '',
                                              ),
                                        ),
                                      );
                                    },
                                    child: CustomSvgImage(
                                        imageName: "applyleave_edit"),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    CustomSvgImage(imageName: "cardcalender"),
                                    const SizedBox(width: 8),
                                    OpenSansText(
                                      formatDateWithoutOrdinal(
                                            absence.creationDate,
                                          ) ??
                                          '',
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      color: AppColors.darkGreyColor,
                                    ),
                                  ],
                                ),
                                if (widget.iconClicked != 'Apply Leave')
                                  DmSansText(
                                    '900',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.headingColor,
                                  )
                              ],
                            ),
                            const SizedBox(height: 4),
                            const Divider(color: AppColors.lightGreyColor3),
                            Padding(
                              padding: const EdgeInsets.only(top: 5),
                              child: Column(
                                children:
                                    getSubValues(
                                          absence.absenceType ?? '',
                                          formatDateWithoutOrdinal(
                                                absence.startDate,
                                              ) +
                                              ' - ' +
                                              formatDateWithoutOrdinal(
                                                absence.endDate,
                                              ),
                                          absence,
                                        ).entries
                                        .map(
                                          (entry) => Padding(
                                            padding: const EdgeInsets.only(
                                              bottom: 12,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                OpenSansText(
                                                  entry.key,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w600,
                                                  color: AppColors.blackColor,
                                                ),
                                                OpenSansText(
                                                  entry.value,
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w400,
                                                  color:
                                                      AppColors.lightGreyColor,
                                                ),
                                              ],
                                            ),
                                          ),
                                        )
                                        .toList(),
                              ),
                            ),
                            if (widget.selectedTabIndex != 1 &&
                                widget.status != 'draft')
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  OpenSansText('Status',
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.blackColor),
                                  OpenSansText(progress,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: progressColor),
                                ],
                              ),
                               const SizedBox(height: 8), 
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
