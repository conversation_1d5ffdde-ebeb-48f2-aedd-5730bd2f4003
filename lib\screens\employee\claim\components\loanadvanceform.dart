import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/customlinkscreen.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/utils/style/colors.dart';

class LoanFormWidget extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final String loanType;
  final VoidCallback? onChanged; // Add callback for form changes
  final bool
      showLoanTypeField; // New parameter to control visibility of loan type field

  const LoanFormWidget({
    Key? key,
    required this.formKey,
    required this.loanType,
    this.onChanged, // Make it optional with default value of null
    this.showLoanTypeField = true, // Default to true
  }) : super(key: key);

  @override
  LoanFormWidgetState createState() => LoanFormWidgetState();

  // Add this method to expose the state publicly
  static LoanFormWidgetState? of(BuildContext context) {
    return context.findAncestorStateOfType<LoanFormWidgetState>();
  }
}

// Making the state class public by removing the underscore
class LoanFormWidgetState extends State<LoanFormWidget> {
  // Define a consistent hint text style
  final TextStyle hintTextStyle = const TextStyle(
    fontFamily: 'Open Sans',
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: AppColors.lightGreyshade,
  );

  List<FileModel> uploadedFiles = [];
  final TextEditingController _loanTypeController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _installmentsController = TextEditingController();
  final TextEditingController _reasonController = TextEditingController();

  bool isFormFilled = false;
  bool skipThisMonth = false;
  bool _isDropdownOpen = false;

  final List<String> loanTypes = ["Salary advance", "Loan against EOS request"];

  String? _currentLoanType;

  @override
  void initState() {
    super.initState();
    _currentLoanType = widget.loanType;
    _loanTypeController.text = widget.loanType;
    
    // Add listeners to trigger state updates
    _amountController.addListener(_checkFormFilled);
    _installmentsController.addListener(_checkFormFilled);
    _reasonController.addListener(_checkFormFilled);
  }

  @override
  void dispose() {
    _loanTypeController.dispose();
    _amountController.dispose();
    _installmentsController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  void _checkFormFilled() {
    setState(() {
      // Different validation logic based on loan type
      if (_currentLoanType == "Loan against EOS request") {
        // For EOS request: amount and reason are required
        isFormFilled = _amountController.text.isNotEmpty &&
            _reasonController.text.isNotEmpty;
      } else {
        // For Salary advance: amount and installments are required
        // (reason is optional for Salary advance)
        isFormFilled = _amountController.text.isNotEmpty &&
            _installmentsController.text.isNotEmpty;
      }
    });

    // Call the onChanged callback to notify parent of changes
    if (widget.onChanged != null) {
      widget.onChanged!();
    }
  }

  // Add method to show bottom sheet
  void _showLoanTypeBottomSheet() async {
    setState(() {
      _isDropdownOpen = true;
    });

    await showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          height: 200, // Adjusted for just two options
          width: double.infinity,
          child: Column(
            children: [
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 30,
                    height: 5,
                    decoration: BoxDecoration(
                      color: AppColors.viewColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              for (var type in loanTypes)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 4,
                  ),
                  child: Material(
                    color:
                        _currentLoanType == type
                            ? AppColors.lightGreyColor2
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(8),
                      splashColor: AppColors.microinteraction,
                      highlightColor: AppColors.microinteraction,
                      onTap: () async {
                        // Let ripple show before pop
                        await Future.delayed(const Duration(milliseconds: 200));
                        setState(() {
                          _currentLoanType = type;
                          _loanTypeController.text = type;
                        });
                        // Check form state after type changes
                        _checkFormFilled();
                        Navigator.pop(context);
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        child: Center(
                          child: Text(
                            type,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight:
                                  _currentLoanType == type
                                      ? FontWeight.w600
                                      : FontWeight.w400,
                              color: AppColors.blackColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    ); // Bottom sheet closed
    if (mounted) {
      setState(() {
        _isDropdownOpen = false;
      });
    }
  }

  // Method to submit form data
 void submitForm() async {
  try {
    if (isFormValid) {
      // Print all form data
      print('=== LOAN/ADVANCE REQUEST SUBMISSION DATA ===');
      print('Loan Type: $_currentLoanType');
      print('Total Amount: ${_amountController.text}');
      
      if (!(_currentLoanType == "Loan against EOS request")) {
        print('Number of Installments: ${_installmentsController.text}');
        print('Skip This Month: $skipThisMonth');
      }
      
      print('Reason: ${_reasonController.text}');
      print('Uploaded Files Count: ${uploadedFiles.length}');
      
      if (uploadedFiles.isNotEmpty) {
        print('Uploaded Files:');
        for (int i = 0; i < uploadedFiles.length; i++) {
          print('  File ${i + 1}: ${uploadedFiles[i].name ?? 'Unknown'}');
        }
      }
      print('============================================');
      
      // Add your submission logic here
      
    } else {
      // Handle case when form is not valid
      print('Form is not valid');
    }
  } catch (e) {
    // Handle any errors that occur during submission
    print('Error during form submission: $e');
  }
}

  @override
  Widget build(BuildContext context) {
    bool isEOSRequest = _currentLoanType == "Loan against EOS request";

    return Padding(
      padding: const EdgeInsets.only(left: 4, right: 4),
      child: Form(
        key: widget.formKey,
        onChanged: () {
          // This will be called whenever any form field changes
          _checkFormFilled();
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showLoanTypeField)
              HeadingText(
                text: "Loan/advance request type",
                hasAsterisk: true,
              ),
            if (widget.showLoanTypeField) const SizedBox(height: 8),
            if (widget.showLoanTypeField)
              _buildLoanTypeField(), // Show loan type field based on the parameter
            if (widget.showLoanTypeField) const SizedBox(height: 24),
            CustomTextFieldWithHeading(
              showSpacing: false,
              Heading: 'Total amount',
              hintText: 'Enter total amount',
              hasAsterisk: true,
              maxlines: 1,
              fillColor: AppColors.whiteColor,
              hintColor: AppColors.lightGreyshade,
              hintStyle: AppColors.lightGreyshade,
              controller: _amountController, // Added controller
            ),
            SizedBox(height: 24),
            // Show Reason field right after Total amount for Loan against EOS request
            if (isEOSRequest) ...[
              CustomTextFieldWithHeading(
                showSpacing: false,
                Heading: "Reason",
                hintText: 'Type here',
                hasAsterisk: true,
                maxlines: 3,
                fillColor: AppColors.whiteColor,
                hintColor: AppColors.lightGreyshade,
                hintStyle: AppColors.lightGreyshade,
                controller: _reasonController, // Added controller
              ),
              SizedBox(height: 24),
            ],

            // No. of installment section only for Salary advance
            if (!isEOSRequest) ...[
              HeadingText(
                text: "No. of installment",
                hasAsterisk: true,
              ),
              const SizedBox(height: 8),
              _buildInstallmentField(),
              const SizedBox(height: 24),
            ],

            HeadingText(text: "Attachments"),
            const SizedBox(height: 8),
            AttachmentField(
              uploadedFiles: uploadedFiles,
              onFilesChanged: (List<FileModel> files) {
                setState(() {
                  uploadedFiles = files;
                });
              }, // Fixed callback
            ),
            const SizedBox(height: 24),

            HeadingText(text: "Links"),
            const SizedBox(height: 8),
            LinkInputList(),
            const SizedBox(height: 24),

            // Show Reason field at the end for Salary advance
            if (!isEOSRequest) ...[
              CustomTextFieldWithHeading(
                showSpacing: false,
                Heading: "Reason",
                hintText: 'Type here',
                hasAsterisk: false,
                maxlines: 3,
                fillColor: AppColors.whiteColor,
                hintColor: AppColors.lightGreyshade,
                hintStyle: AppColors.lightGreyshade,
                controller: _reasonController, // Added controller
              ),
                const SizedBox(height: 32),
            ],
          ],
        ),
      ),
    );
  }

  // Getter to check if form is valid
  bool get isFormValid {
    if (_currentLoanType == "Loan against EOS request") {
      // For EOS request: amount and reason are required
      return _amountController.text.isNotEmpty &&
          _reasonController.text.isNotEmpty;
    } else {
      // For Salary advance: amount and installments are required
      return _amountController.text.isNotEmpty &&
          _installmentsController.text.isNotEmpty;
    }
  }

  Widget _buildLoanTypeField() {
    return SizedBox(
      height: 43,
      width: double.infinity,
      child: TextFormField(
        controller: _loanTypeController,
        readOnly: true,
        onTap: _showLoanTypeBottomSheet,
        decoration: InputDecoration(
          hintText: 'Select loan type',
          hintStyle: hintTextStyle,
          fillColor: AppColors.whiteColor,
          filled: true,
          suffixIcon: Padding(
            padding: const EdgeInsets.only(right: 12),
            child: CustomSvgImage(
              imageName: _isDropdownOpen ? "Arrowup" : "ArrowLeft",
              color: AppColors.viewColor,
            ),
          ),
          suffixIconConstraints: const BoxConstraints(
            minHeight: 24,
            minWidth: 24,
          ),

          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppColors.lightGreyColor2,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppColors.lightGreyColor2,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppColors.lightGreyColor2,
              width: 1,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInstallmentField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 43,
                width: double.infinity,
                child: TextFormField(
                  controller: _installmentsController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: "Enter no. of installment",
                    hintStyle: hintTextStyle, // Apply consistent hint style
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 15,
                    ),
                    fillColor: AppColors.whiteColor,
                    filled: true,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppColors.lightGreyColor2,
                        width: 1,
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppColors.lightGreyColor2,
                        width: 1,
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                        color: AppColors.lightGreyColor2,
                        width: 1,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 20),
            Container(
              height: 24,
              width: 24,
              decoration: BoxDecoration(
                color: AppColors.viewColor,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Checkbox(
                value: skipThisMonth,
                onChanged: (bool? value) {
                  setState(() {
                    skipThisMonth = value ?? false;
                  });
                },
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                checkColor: AppColors.whiteColor,
                fillColor: WidgetStateProperty.all(AppColors.viewColor),
                side: BorderSide.none, // Removes black outline
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              "Skip this month",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: AppColors.blackColor,
                fontFamily: 'open sans',
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        const Text(
          "By selecting 'Skip this month' you agree to skip the payment for the current month.",
          style: TextStyle(
            fontSize: 12,
            color: AppColors.viewColor,
            fontWeight: FontWeight.w400,
            fontFamily: 'open sans',
          ),
        ),
      ],
    );
  }
}
