import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/calendar/meetingDetails.dart';
import 'package:seawork/screens/employee/employee/providers/meetingProvider.dart';
import 'package:seawork/screens/employee/employee/models/meeting.dart';
import 'package:seawork/utils/util.dart';

class DayScreen extends ConsumerWidget {
  final DateTime selectedDate;

  const DayScreen({Key? key, required this.selectedDate}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final calendarState = ref.watch(graphCalendarProvider);
    final List<Meeting> allMeetings = calendarState.meetings;

    final Map<String, List<Meeting>> meetingsByDate = {};
    for (final meeting in allMeetings) {
      if (!meetingsByDate.containsKey(meeting.date)) {
        meetingsByDate[meeting.date] = [];
      }
      meetingsByDate[meeting.date]!.add(meeting);
    }

    final dateString = _formatDateForComparison(selectedDate);
    final meetings = meetingsByDate[dateString] ?? [];
    meetings.sort((a, b) => a.time.compareTo(b.time));

    return Container(
      color: AppColors.secondaryColor,
      child: Column(
        children: [
          Expanded(child: _buildDayTimeline(meetings, selectedDate, context)),
        ],
      ),
    );
  }

  // Helper method to convert UTC time to local time
  DateTime _convertToLocalTime(String utcTimeString, DateTime meetingDate) {
    try {
      // Parse the UTC time string (assuming format is "HH:mm")
      final timeParts = utcTimeString.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      // Create a DateTime object in UTC
      final utcDateTime = DateTime.utc(
        meetingDate.year,
        meetingDate.month,
        meetingDate.day,
        hour,
        minute,
      );

      // Convert to local time zone
      return utcDateTime.toLocal();
    } catch (e) {
      print('Error converting time: $e');
      return meetingDate; // Fallback to meeting date if conversion fails
    }
  }

  // Format time for display in user's local timezone
  String _formatTimeForDisplay(String time, DateTime meetingDate) {
    final localTime = _convertToLocalTime(time, meetingDate);
    return DateFormat.jm().format(localTime); // Formats like "2:30 PM"
  }

  // Detailed timeline for day view
  Widget _buildDayTimeline(
    List<Meeting> meetings,
    DateTime selectedDate,
    BuildContext context,
  ) {
    return Stack(
      children: [
        // Vertical timeline line
        Positioned.fill(
          left: 66,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Align(
              alignment: Alignment.topLeft,
              child: Container(
                width: 2,
                color: AppColors.calandertimelinecolor,
              ),
            ),
          ),
        ),

        // Hour blocks
        ListView.builder(
          itemCount: 25,
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemBuilder: (context, hour) {
            final hourLabel = DateFormat.j().format(DateTime(0, 1, 1, hour));
            final hourMeetings =
                meetings.where((meeting) {
                  final localTime = _convertToLocalTime(
                    meeting.time,
                    selectedDate,
                  );
                  return localTime.hour == hour;
                }).toList();

            const double hourBlockHeight = 90;
            final double spacing = 6.0;

            // Dynamically calculate card height
            double totalSpacing = (hourMeetings.length - 1) * spacing;
            double cardHeight =
                hourMeetings.isNotEmpty
                    ? ((hourBlockHeight - totalSpacing - 20) /
                        hourMeetings.length)
                    : 0;

            return SizedBox(
              height: hourBlockHeight,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 28),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Hour label + horizontal line
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 38,
                          child: DmSansText(
                            hourLabel,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: AppColors.viewColor,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Container(
                            height: 1,
                            color: AppColors.calenderDaydivColor,
                          ),
                        ),
                      ],
                    ),

                    // Meeting cards
                    if (hourMeetings.isNotEmpty)
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 40.0, top: 4),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children:
                                hourMeetings.map((meeting) {
                                  return Padding(
                                    padding: EdgeInsets.only(
                                      bottom:
                                          meeting == hourMeetings.last
                                              ? 0
                                              : spacing,
                                    ),
                                    child: _buildDayMeetingItem(
                                      meeting,
                                      selectedDate,
                                      context,
                                    ),
                                  );
                                }).toList(),
                          ),
                        ),
                      )
                    else
                      const SizedBox(height: 20),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  // Meeting item for day view
  Widget _buildDayMeetingItem(
    Meeting meeting,
    DateTime selectedDate,
    BuildContext context,
  ) {
    String locationText = "Microsoft Teams";

    if (meeting.location != null && meeting.location!.isNotEmpty) {
      locationText = meeting.location!;
    } else if (meeting.isOnline && meeting.onlineMeetingProvider.isNotEmpty) {
      if (meeting.onlineMeetingProvider == 'teamsForBusiness') {
        locationText = "Microsoft Teams";
      } else {
        locationText = meeting.onlineMeetingProvider;
      }
    }

    return Material(
      color: AppColors.transparentColor,
      child: InkWell(
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
        splashColor: AppColors.microinteraction,
        highlightColor: AppColors.microinteraction,
        onTap: () async {
          await Future.delayed(const Duration(milliseconds: 150));
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => MeetingDetailsScreen(meeting: meeting),
            ),
          );
        },
        child: Ink(
          decoration: BoxDecoration(
            color: AppColors.calendermeetColor,
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(8),
              bottomRight: Radius.circular(8),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.boxshadow.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DmSansText(
                meeting.title,
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color:
                    meeting.isCancelled
                        ? AppColors.darkGreyColor
                        : AppColors.blackColor,
                maxLines: 1,
                textDecoration:
                    meeting.isCancelled
                        ? TextDecoration.lineThrough
                        : TextDecoration.none,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: DmSansText(
                      capitalizeFirstWordOnly(locationText),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color:
                          meeting.isCancelled
                              ? AppColors.darkGreyColor
                              : AppColors.blackColor,
                      maxLines: 1,
                      textDecoration:
                          meeting.isCancelled
                              ? TextDecoration.lineThrough
                              : TextDecoration.none,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDateForComparison(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }
}
