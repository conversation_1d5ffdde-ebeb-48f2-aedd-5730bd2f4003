import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';

class CustomCalendar extends StatefulWidget {
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(DateTime) onStartDateSelected;
  final Function(DateTime) onEndDateSelected;
  final VoidCallback onApplyFilter;
  final VoidCallback onClearFilters;

  const CustomCalendar({
    Key? key,
    this.startDate,
    this.endDate,
    required this.onStartDateSelected,
    required this.onEndDateSelected,
    required this.onApplyFilter,
    required this.onClearFilters,
  }) : super(key: key);

  @override
  _CustomCalendarState createState() => _CustomCalendarState();
}

class _CustomCalendarState extends State<CustomCalendar> {
  late DateTime _currentMonth;
  Set<DateTime> _selectedDates = {};

  @override
  void initState() {
    super.initState();
    _currentMonth = widget.startDate ?? DateTime.now();
    
    if (widget.startDate != null) {
      _selectedDates.add(widget.startDate!);
    }
    if (widget.endDate != null) {
      _selectedDates.add(widget.endDate!);
    }
  }

  void _previousMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    });
  }

  void _nextMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    });
  }

  void _selectDate(DateTime date) {
    setState(() {
      final normalizedDate = DateTime(date.year, date.month, date.day);
      
      if (_selectedDates.contains(normalizedDate)) {
        _selectedDates.remove(normalizedDate);
      } else {
        _selectedDates.add(normalizedDate);
      }
    });
  }

  bool _isDateSelected(DateTime date) {
    final normalizedDate = DateTime(date.year, date.month, date.day);
    return _selectedDates.contains(normalizedDate);
  }

  void _applySelection() {
    if (_selectedDates.isNotEmpty) {
      final sortedDates = _selectedDates.toList()..sort();
      widget.onStartDateSelected(sortedDates.first);
      widget.onEndDateSelected(sortedDates.last);
    }
    widget.onApplyFilter();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.boxshadow.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Month navigation header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: _previousMonth,
                icon: Icon(Icons.chevron_left, color: AppColors.chevroncolor),
                iconSize: 28,
              ),
              DmSansText(
                DateFormat('MMMM yyyy').format(_currentMonth),
                color: AppColors.blackColor,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              IconButton(
                onPressed: _nextMonth,
                icon: Icon(Icons.chevron_right, color: AppColors.chevroncolor),
                iconSize: 28,
              ),
            ],
          ),
          
          // Divider line
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18),
            child: Divider(
              color: AppColors.dividercolor,
              thickness: 1,
              height: 20,
            ),
          ),
          
          SizedBox(height: 16),
          
          // Weekday headers
          Row(
            children: ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT']
                .map((day) => Expanded(
                      child: Center(
                        child: OpenSansText(
                          day,
                          color: AppColors.lightBlack,
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ))
                .toList(),
          ),
          
          SizedBox(height: 8),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.3,
            child: _buildCalendarGrid(),
          ),
          
          SizedBox(height: 16),
          
          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton(
                onPressed: _applySelection,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.viewColor,
                  padding: EdgeInsets.symmetric(horizontal: 42, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: DmSansText(
                  'Done',
                  color: AppColors.whiteColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarGrid() {
    final firstDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final lastDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 0);
    final startingWeekday = firstDayOfMonth.weekday % 7;
    final daysInMonth = lastDayOfMonth.day;

    List<Widget> dayWidgets = [];

    for (int i = 0; i < startingWeekday; i++) {
      dayWidgets.add(Container());
    }

    for (int day = 1; day <= daysInMonth; day++) {
      final currentDate = DateTime(_currentMonth.year, _currentMonth.month, day);
      final isSelected = _isDateSelected(currentDate);

      dayWidgets.add(
        GestureDetector(
          onTap: () => _selectDate(currentDate),
          child: Container(
            margin: EdgeInsets.all(5),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: isSelected
                  ? Border.all(color: AppColors.viewColor, width: 2)
                  : null,
            ),
            child: Center(
              child: OpenSansText(
                day.toString(),
                color: isSelected
                    ? AppColors.viewColor
                    : AppColors.blackColor,
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ),
        ),
      );
    }

    final rows = ((dayWidgets.length) / 7).ceil();
    final gridHeight = rows * 50.0;

    return SizedBox(
      height: gridHeight,
      child: GridView.count(
        physics: NeverScrollableScrollPhysics(),
        crossAxisCount: 7,
        childAspectRatio: 1.0,
        children: dayWidgets,
      ),
    );
  }
}