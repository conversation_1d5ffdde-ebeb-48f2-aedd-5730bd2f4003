import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';

class StatusTab {
  final String label;
  final int? count;

  StatusTab({required this.label, this.count});
}

class CustomStatusTabBar extends StatefulWidget {
  final TabController controller;
  final List<StatusTab> tabs;

  const CustomStatusTabBar({
    Key? key,
    required this.controller,
    required this.tabs,
  }) : super(key: key);

  @override
  State<CustomStatusTabBar> createState() => _CustomStatusTabBarState();
}

class _CustomStatusTabBarState extends State<CustomStatusTabBar> {
  late int selectedIndex;

  @override
  void initState() {
    super.initState();
    selectedIndex = widget.controller.index;

    widget.controller.addListener(() {
      if (mounted && selectedIndex != widget.controller.index) {
        setState(() {
          selectedIndex = widget.controller.index;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: TabBar(
        controller: widget.controller,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        indicatorColor: AppColors.viewColor,
        labelColor: AppColors.viewColor,
        unselectedLabelColor: AppColors.blackColor,
        splashFactory: NoSplash.splashFactory,
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        dividerColor: Colors.transparent,
        indicator: const UnderlineTabIndicator(
          borderSide: BorderSide(width: 2.0, color: AppColors.viewColor),
          insets: EdgeInsets.only(top: 12),
        ),
        padding: EdgeInsets.zero,
        labelPadding: const EdgeInsets.only(right: 32),
        tabs: List.generate(widget.tabs.length, (index) {
          final tab = widget.tabs[index];
          final isSelected = selectedIndex == index;

          final countText = '${tab.count}';
          final digitLength = countText.length;

          double circleSize = 16;
          double fontSize = 10;

          if (digitLength >= 3) {
            circleSize = 20;
            fontSize = 11;
          }
          if (digitLength >= 4) {
            circleSize = 24;
            fontSize = 12;
          }
          final paddingBefore = index == 0 ? 8.0 : 12.0;

          return Tab(
            child: Padding(
              padding: EdgeInsets.only(left: paddingBefore),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  OpenSansText(
                    tab.label,
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                    color:
                        isSelected ? AppColors.viewColor : AppColors.blackColor,
                  ),
                  const SizedBox(width: 8),
                  if (tab.count != null)
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Container(
                          width: circleSize,
                          height: circleSize,
                          decoration: const BoxDecoration(
                            color: AppColors.Orange,
                            shape: BoxShape.circle,
                          ),
                        ),
                        Center(
                          child: RobotoText(
                            '$countText',
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                            color: AppColors.whiteColor,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}
