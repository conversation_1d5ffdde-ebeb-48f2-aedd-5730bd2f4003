import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/customDatePickerField.dart';
import 'package:seawork/components/widget/customTextFieldWithHeading.dart';
import 'package:seawork/components/widget/customlinkscreen.dart';
import 'package:seawork/components/widget/headingText.dart';

class FormData {
  final String? courseName;
  final String? fromDate;
  final String? toDate;
  final String? venue;
  final String? cost;
  final String? yearOfClaim;
  final String? shippedFrom;
  final String? weight;
  final String? expenseType;
  final String? paymentMode;
  final String? amount;
  final List<String> links;
  final String? comments;
  final List<FileModel> attachments;
  final String? justification;
  final String? reimbursementType;

  FormData({
    this.courseName,
    this.fromDate,
    this.toDate,
    this.venue,
    this.cost,
    this.yearOfClaim,
    this.shippedFrom,
    this.weight,
    this.expenseType,
    this.paymentMode,
    this.amount,
    required this.links,
    this.comments,
    required this.attachments,
    this.justification,
    this.reimbursementType,
  });
}

class ReimbursementForm extends StatefulWidget {
  final String reimbursementType;
  final Function(FormData formData) onSubmit;
  final bool showDropdown; // New parameter to control dropdown visibility

  const ReimbursementForm({
    Key? key,
    required this.reimbursementType,
    required this.onSubmit,
    this.showDropdown = true, // Default to true
  }) : super(key: key);

  @override
  State<ReimbursementForm> createState() => _ReimbursementFormState();
}

class _ReimbursementFormState extends State<ReimbursementForm> {
  final List<String> reimbursementTypes = [
    "Training or conference reimbursement",
    "Transportation reimbursement",
    "Visa expense reimbursement",
    "Shipping reimbursement",
    "General reimbursement",
    "Medical bill reimbursement",
  ];

  final TextStyle hintTextStyle = const TextStyle(
    fontFamily: 'Open Sans',
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: AppColors.lightGreyshade,
  );

  String _selectedType = "";
  List<FileModel> uploadedFiles = [];
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final TextEditingController _typeController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController endDateController = TextEditingController();
  final TextEditingController _toDateController = TextEditingController();
  final TextEditingController _courseNameController = TextEditingController();
  final TextEditingController _venueController = TextEditingController();
  final TextEditingController _costController = TextEditingController();
  List<TextEditingController> _linkControllers = [];
  final TextEditingController _justificationController =
      TextEditingController();
  final TextEditingController _yearOfClaimController = TextEditingController();
  final TextEditingController _commentsController = TextEditingController();
  final TextEditingController _shippedFromController = TextEditingController();
  final TextEditingController _weightController = TextEditingController();
  final TextEditingController _expenseTypeController = TextEditingController();
  final TextEditingController _paymentModeController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();

  bool isFormValid = false;
  bool isFormFilled = false;
  bool _isBottomSheetOpen = false;

  // Method to handle reimbursement type changes
  void _handleReimbursementTypeChange(String type) {
    setState(() {
      _selectedType = type;
      _typeController.text = type;
      uploadedFiles.clear();
      // Check if the type is General reimbursement
      if (type.toLowerCase() == "general reimbursement") {
        // Set payment mode to Bank transfer
        _paymentModeController.text = "Bank transfer";
      }
    });
  }

  @override
  void initState() {
    super.initState();
    // Use the method to handle initial type setting
    _handleReimbursementTypeChange(widget.reimbursementType);
  }

  void _showReimbursementTypeBottomSheet() {
    setState(() {
      _isBottomSheetOpen = true;
    });

    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allow the sheet to be scrollable if needed
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        // Calculate the item height based on number of options
        final itemHeight = 56.0; // Height of each option item
        final headerHeight = 40.0; // Height for the drag handle area
        final bottomPadding =
            MediaQuery.of(context).padding.bottom; // Safe area padding

        // Calculate total height (items + header + bottom padding + some extra space)
        final totalHeight = (itemHeight * reimbursementTypes.length) +
            headerHeight +
            bottomPadding +
            16.0; // Extra padding

        return Container(
          decoration: const BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          // Use constraints to prevent overflow
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height *
                0.7, // Max 70% of screen height
          ),
          width: double.infinity,
          child: Column(
            mainAxisSize:
                MainAxisSize.min, // Important - only take needed space
            children: [
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 30,
                    height: 5,
                    decoration: BoxDecoration(
                      color: AppColors.viewColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Use Expanded with SingleChildScrollView to handle content that might overflow
              Flexible(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    for (var type in reimbursementTypes)
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 0,
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            color: _selectedType == type
                                ? AppColors.lightGreyColor2
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ListTile(
                            title: OpenSansText(
                              type,
                              textAlign: TextAlign.center,
                              fontSize: _selectedType == type ? 16 : 14,
                              fontWeight: _selectedType == type
                                  ? FontWeight.w600
                                  : FontWeight.w400,
                              color: AppColors.blackColor,
                              letterSpacing: 0,
                            ),
                            onTap: () {
                              // Use the centralized method to handle type change
                              _handleReimbursementTypeChange(type);
                              Navigator.pop(context);
                            },
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              // Add padding at the bottom to account for safe area
              SizedBox(height: bottomPadding),
            ],
          ),
        );
      },
    ).whenComplete(() {
      setState(() {
        _isBottomSheetOpen = false;
      });
    });
  }

  void _handleSubmit() {
    if (_formKey.currentState!.validate()) {
      final formData = FormData(
        courseName: _courseNameController.text,
        fromDate: _dateController.text,
        toDate: _toDateController.text,
        venue: _venueController.text,
        cost: _costController.text,
        yearOfClaim: _yearOfClaimController.text,
        shippedFrom: _shippedFromController.text,
        weight: _weightController.text,
        expenseType: _expenseTypeController.text,
        paymentMode: _paymentModeController.text,
        amount: _amountController.text,
        links: _linkControllers
            .map((c) => c.text)
            .where((t) => t.isNotEmpty)
            .toList(),
        comments: _commentsController.text,
        attachments: uploadedFiles,
        justification: _justificationController.text,
        reimbursementType: _selectedType,
      );
      widget.onSubmit(formData);
    }
  }

  Widget _buildDropdownField() {
    return SizedBox(
      height: 48,
      width: double.infinity,
      child: TextFormField(
        controller: _typeController,
        readOnly: true,
        onTap: _showReimbursementTypeBottomSheet,
        decoration: InputDecoration(
          hintText: 'Select type',
          hintStyle: hintTextStyle,
          fillColor: AppColors.whiteColor,
          filled: true,
          suffixIcon: Padding(
            padding: const EdgeInsets.only(right: 12),
            child: CustomSvgImage(
              imageName: _isBottomSheetOpen ? "Arrowup" : "ArrowLeft",
              color: AppColors.viewColor,
            ),
          ),
          suffixIconConstraints: const BoxConstraints(
            minHeight: 24,
            minWidth: 24,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 12,
          ),
          isCollapsed: false,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppColors.lightGreyColor2,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppColors.lightGreyColor2,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: AppColors.lightGreyColor2,
              width: 1,
            ),
          ),
        ),
        style: const TextStyle(
          fontFamily: 'Open Sans',
          fontSize: 14,
          fontWeight: FontWeight.w400,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    bool isTransportationReimbursement =
        _selectedType.toLowerCase() == "transportation reimbursement";
    bool isMedicalReimbursement =
        _selectedType.toLowerCase() == "medical bill reimbursement";
    bool isVisaExpense =
        _selectedType.toLowerCase() == "visa expense reimbursement";
    bool isShippingReimbursement =
        _selectedType.toLowerCase() == "shipping reimbursement";
    bool isGeneralReimbursement =
        _selectedType.toLowerCase() == "general reimbursement";

    // Double-check payment mode for General reimbursement
    if (isGeneralReimbursement &&
        _paymentModeController.text != "Bank transfer") {
      _paymentModeController.text = "Bank transfer";
    }

    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20, top: 24),
      child: Column(
        children: [
          Expanded(
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.showDropdown)
                    HeadingText(
                      text: 'Reimbursement request type',
                      hasAsterisk: true,
                    ),
                  const SizedBox(height: 8),
                  if (widget.showDropdown)
                    _buildDropdownField(), // Conditional rendering
                  if (widget.showDropdown) const SizedBox(height: 24),

                  if (!isTransportationReimbursement &&
                      !isVisaExpense &&
                      !isShippingReimbursement &&
                      !isGeneralReimbursement &&
                      !isMedicalReimbursement) ...[
                    // const SizedBox(height: 24),
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: "Course name",
                      hintText: 'Enter course name',
                      hasAsterisk: true,
                      maxlines: 1,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    CustomDatePickerField(
                      dateText: 'From date',
                      hintText: 'Enter from date',
                      controller: _dateController,
                      onDateSelected: (date) {},
                      isStartDate: true,
                    ),
                    CustomDatePickerField(
                      dateText: 'To date',
                      hintText: 'Enter to date',
                      controller: endDateController,
                      onDateSelected: (date) {},
                      isStartDate: false,
                    ),
                    const SizedBox(height: 24),
                    if (!isShippingReimbursement) ...[
                      CustomTextFieldWithHeading(
                        showSpacing: false,
                        Heading: "Venue / location",
                        hintText: 'Enter venue',
                        hasAsterisk: true,
                        maxlines: 1,
                        fillColor: AppColors.whiteColor,
                        hintColor: AppColors.lightGreyshade,
                        hintStyle: AppColors.lightGreyshade,
                      ),
                    ],
                  ],
                  if (!isGeneralReimbursement) ...[
                    // Only add spacing if heading is "Cost", not "Total amount"
                    if (!(isShippingReimbursement ||
                        isVisaExpense ||
                        isMedicalReimbursement ||
                        isTransportationReimbursement))
                      const SizedBox(height: 24),
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: (isShippingReimbursement ||
                              isVisaExpense ||
                              isMedicalReimbursement ||
                              isTransportationReimbursement)
                          ? "Total amount"
                          : "Cost",
                      hintText: (isShippingReimbursement ||
                              isVisaExpense ||
                              isMedicalReimbursement ||
                              isTransportationReimbursement)
                          ? 'Enter amount'
                          : 'Enter Cost',
                      hasAsterisk: true,
                      maxlines: 1,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    const SizedBox(height: 24),
                  ],
                  if (isGeneralReimbursement) ...[
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: "Type of expense",
                      hintText: 'Enter expense type',
                      hasAsterisk: true,
                      maxlines: 1,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    const SizedBox(height: 24),
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: "Payment mode",
                      hintText: 'Enter payment mode',
                      controller:
                          _paymentModeController, // Important: Link to the controller
                      hasAsterisk: true,
                      maxlines: 1,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                      readOnly: true,
                    ),
                    const SizedBox(height: 24),
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: "Amount in AED",
                      hintText: 'Enter amount in AED',
                      hasAsterisk: true,
                      maxlines: 1,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    const SizedBox(height: 24),
                  ],
                  if (isShippingReimbursement) ...[
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: "Shipped from",
                      hintText: 'Enter location',
                      hasAsterisk: true,
                      maxlines: 1,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    const SizedBox(height: 24),
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: "Weight of Items being shipped in kg",
                      hintText: 'Enter weight',
                      hasAsterisk: true,
                      maxlines: 1,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    const SizedBox(height: 24),
                  ],

                  HeadingText(text: 'Attachment', hasAsterisk: true),
                  // const SizedBox(height: 8),
                  AttachmentField(
                    uploadedFiles: uploadedFiles,
                    onFilesChanged: (List<FileModel> files) {
                      setState(() {
                        uploadedFiles = files;
                      });
                    },
                  ),
                  const SizedBox(height: 24),

                  HeadingText(text: 'Link'),
                  const SizedBox(height: 8),
                  LinkInputList(),
                  const SizedBox(height: 16),

                  if (isTransportationReimbursement) ...[
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: isTransportationReimbursement
                          ? "Business purpose of trip"
                          : "Medical justification",
                      hintText: 'Enter',
                      hasAsterisk: true,
                      maxlines: 3,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    const SizedBox(height: 8),
                  ] else if (!isVisaExpense && !isShippingReimbursement) ...[
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading:
                          isGeneralReimbursement ? "Comments" : "Justification",
                      hintText: isGeneralReimbursement
                          ? 'Enter comments'
                          : 'Enter justification',
                      hasAsterisk: isGeneralReimbursement,
                      // hasAsterisk: true,
                      maxlines: 3,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    const SizedBox(height: 24),
                  ],
                  if (isShippingReimbursement) ...[
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: isShippingReimbursement
                          ? "Comments"
                          : "Business purpose of trip",
                      hintText: 'Type here',
                      hasAsterisk: true,
                      maxlines: 3,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                  ],
                  if (isVisaExpense) ...[
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: "Year of claim",
                      hintText: 'Enter year',
                      hasAsterisk: true,
                      maxlines: 1,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    const SizedBox(height: 24),
                    CustomTextFieldWithHeading(
                      showSpacing: false,
                      Heading: 'Comments',
                      hintText: 'Add comments here',
                      hasAsterisk: true,
                      maxlines: 3,
                      fillColor: AppColors.whiteColor,
                      hintColor: AppColors.lightGreyshade,
                      hintStyle: AppColors.lightGreyshade,
                    ),
                    const SizedBox(height: 24),
                  ],
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 16.0, bottom: 32.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isFormFilled ? _handleSubmit : null,
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(double.infinity, 56),
                  backgroundColor: isFormFilled
                      ? AppColors.viewColor
                      : AppColors.lightGreyColor2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: DmSansText(
                  'Submit request',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.whiteColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _typeController.dispose();
    _dateController.dispose();
    _toDateController.dispose();
    _courseNameController.dispose();
    _venueController.dispose();
    _costController.dispose();
    for (var controller in _linkControllers) {
      controller.dispose();
    }
    _justificationController.dispose();
    _yearOfClaimController.dispose();
    _commentsController.dispose();
    _shippedFromController.dispose();
    _weightController.dispose();
    _expenseTypeController.dispose();
    _paymentModeController.dispose();
    _amountController.dispose();
    super.dispose();
  }
}
