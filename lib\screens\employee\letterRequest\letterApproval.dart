
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/box/container.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/scroll/scrollPaginationContainer.dart';
import 'package:seawork/components/widget/customLoaderWithMessages.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/components/widget/noDataFound.dart';
import 'package:seawork/screens/dashboard/mainDashboard/mainDashboard.dart';
import 'package:seawork/screens/employee/absence/models/userInfo.dart';
import 'package:seawork/screens/employee/letterRequest/components/documentListItem.dart';
import 'package:seawork/screens/employee/letterRequest/components/request_letter_bottom_sheet.dart';
import 'package:seawork/screens/employee/letterRequest/providers/letter_request_types_provider.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/widget/customTabbar.dart';

// Add this provider if not already defined
final dateRangeProvider = StateProvider<Map<String, DateTime>?>((ref) => null);

class LetterApprovals extends ConsumerStatefulWidget {
  final String iconClicked;
  static String tag = 'letterRequestTest';
  const LetterApprovals({Key? key, required this.iconClicked}) : super(key: key);

  @override
  ConsumerState<LetterApprovals> createState() => _LetterApprovalsState();
}

class _LetterApprovalsState extends ConsumerState<LetterApprovals> {
  int _selectedTabIndex = 0;
  String _selectedStatus = "pending";
  String _renderingStatus = '';
  String _searchQuery = "";
  bool isLoading = false;

  void _handleSearchQueryChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    final notifier = _selectedStatus == "pending"
        ? ref.read(letterRequestPendingProvider.notifier)
        : ref.read(letterRequestApprovedProvider.notifier);
    notifier.search(query);
  }

  void _clearFilters() {
    ref.read(dateRangeProvider.notifier).state = null;
    ref.refresh(letterRequestPendingProvider);
    ref.refresh(letterRequestApprovedProvider);
    _handleSearchQueryChanged(""); // Clear search query
  }

  @override
  Widget build(BuildContext context) {
    final pendingState = ref.watch(letterRequestPendingProvider);
    final approvedState = ref.watch(letterRequestApprovedProvider);
    final statusProvider = ref.watch(documentStatusProvider);

    final paginationState = statusProvider == "pending" ? pendingState : approvedState;

    final notifier = statusProvider == "pending"
        ? ref.read(letterRequestPendingProvider.notifier)
        : ref.read(letterRequestApprovedProvider.notifier);

    final documentsAsync = ref.watch(
      GetDocumentRecordStatusProvider(int.parse(masriPersonId)),
    );

    return SecondaryScaffoldWithAppBar(
      context,
      showHelpIcon: true,
      widget.iconClicked,
      SvgImage24x24('assets/images/appbackbutton.svg'),
      () {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => MainDashboard()),
        );
      },
      bodyItem: Padding(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top,
          bottom: MediaQuery.of(context).padding.bottom,
        ),
        child: Column(
          children: [
            SizedBox(height: 8),
            
            // Date range filter display
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 20),
              child: Consumer(
                builder: (context, ref, child) {
                  final dateRange = ref.watch(dateRangeProvider);
                  if (dateRange == null ||
                      dateRange['startDate'] == null ||
                      dateRange['endDate'] == null) {
                    return const SizedBox.shrink();
                  }

                  final startDate = DateFormat('dd MMM yyyy').format(dateRange['startDate']!);
                  final endDate = DateFormat('dd MMM yyyy').format(dateRange['endDate']!);

                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        OpenSansText(
                          '$startDate - $endDate',
                          fontSize: 14,
                          color: AppColors.viewColor,
                          fontWeight: FontWeight.w400,
                        ),
                        TextButton(
                          onPressed: _clearFilters,
                          child: OpenSansText(
                            'clear filters',
                            fontSize: 12,
                            color: AppColors.viewColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            
            if (!isCreatedByMeOnly)
              Padding(
                padding: const EdgeInsets.only(left: 20, right: 20),
                child: Container(
                  width: double.infinity,
                  height: 38,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedTabIndex = 0;
                            });
                            print('Clicked tab = $_selectedTabIndex');
                          },
                          child: _buildTabItem('Created by me', 0, '9'),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedTabIndex = 1;
                            });
                            print('Clicked tab = $_selectedTabIndex');
                          },
                          child: _buildTabItem('Assigned to me', 1, '5'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            SizedBox(height: 8),
            
            if (_selectedTabIndex == 0)
              DefaultTabController(
                length: 5,
                child: Expanded(
                  child: documentsAsync.when(
                    data: (docStatus) {
                      return CustomTabBar(
                        counts: [
                          docStatus.pendingItems,
                          docStatus.approvedItems,
                        ],
                        onTabChanged: (status) {
                          ref.read(documentStatusProvider.notifier).state = status;
                        },
                        length: 2,
                        iconClicked: 'Letter request',
                        onSearchQueryChanged: _handleSearchQueryChanged,
                        buildTabContent: (
                          String status, {
                          int? selectedTabIndex,
                          String? iconClicked,
                        }) {
                          return Column(
                            children: [
                              const SizedBox(height: 10),
                              Expanded(
                                child: paginationState.isLoading
                                    ? const Center(
                                        child: CustomLoadingWidget(),
                                      )
                                    : ScrollPaginationContainer(
                                        onRefresh: () async {
                                          await notifier.refresh();
                                        },
                                        onLoadMore: () async {
                                          await notifier.loadMoreRecords();
                                        },
                                        isLoading: paginationState.isLoading,
                                        hasMore: paginationState.hasMore,
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                        ),
                                        children: [
                                          DocumentListItem(
                                            documentList: paginationState.records,
                                            status: status,
                                            showDownloadButton: true,
                                            selectedStatus: _selectedStatus,
                                            selectedTabIndex: 0,
                                            iconClicked: 'Letter request',
                                            isVisible: false,
                                            hideContainerForScreens: true,
                                            removeTopBottomPadding: true,
                                          ),
                                        ],
                                        noMoreDataIndicator:
                                            (!paginationState.isLoading &&
                                                    !paginationState.hasMore &&
                                                    paginationState.records.isEmpty)
                                                ? (_searchQuery.isNotEmpty
                                                    ? noSearchResultWidget(context)
                                                    : noDataFoundWidget(context))
                                                : null,
                                      ),
                              ),
                            ],
                          );
                        },
                      );
                    },
                    loading: () => const Center(child: RotatingLoaderWithMessages()),
                    error: (err, stack) => Center(
                      child: 
                      OpenSansText(
                        'Something went wrong. Please try again later.',fontSize: 12,
                      )
                    ),
                  ),
                ),
              ),
            if (_selectedTabIndex == 1)
              DefaultTabController(
                length: 3,
                child: Expanded(
                  child: CustomTabBar(
                    onTabChanged: (status) {
                      setState(() {
                        _selectedStatus = status;
                      });
                    },
                    length: 3,
                    iconClicked: 'Letter Request',
                    buildTabContent: (
                      String status, {
                      int? selectedTabIndex,
                      String? iconClicked,
                    }) {
                      return SizedBox(
                        height: 16,
                      );
                    },
                  ),
                ),
              ),
          ],
        ),
      ),
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(bottom: 52.0),
        child: FloatingActionButton(
          onPressed: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              isDismissible: true,
              enableDrag: true,
              builder: (context) {
                return DraggableScrollableSheet(
                  initialChildSize: 0.6,
                  minChildSize: 0.3,
                  maxChildSize: 0.85,
                  expand: false,
                  builder: (context, scrollController) {
                    return ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20.0),
                        topRight: Radius.circular(20.0),
                      ),
                        child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white, 
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(20.0),
                          topRight: Radius.circular(20.0),
                        ),
                      ),
                      child: RequestLetterBottomSheet(
                        'all',
                        scrollController: scrollController,
                      ),
                        ),
                    );
                  },
                );
              },
            );
          },
          shape: const CircleBorder(),
          child: ClipOval(child: CustomSvgImage(imageName: "add_icon")),
        ),
      ),
      bottomNavigationBar: CustomBottomNavigationBar(
        onTap: (index) {
          print('Bottom navigation item tapped: $index');
        },
      ),
    );
  }

  Widget _buildTabItem(String title, int index, String count) {
    return Container(
      height: 30,
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: _selectedTabIndex == index
            ? AppColors.viewColor
            : AppColors.transparentColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            OpenSansText(
              title,
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: _selectedTabIndex == index
                  ? AppColors.whiteColor
                  : AppColors.viewColor,
            ),
            const SizedBox(width: 8),
            Stack(
              alignment: Alignment.center,
              children: [
                CustomPngImage(
                  imageName: "circleorange",
                  height: 14,
                  width: 14,
                ),
              
                RobotoText(
                   count,
                   fontSize: 10,
                    fontWeight: FontWeight.w400,
                    color: AppColors.whiteColor,
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class StaticDocumentTypes {
  static const List<String> documentTypes = [
    "Request Bank Change",
    "Request Business Card",
    "Request Embassy Letter",
    "Request Medical Fitness Letter",
    "Request NOC for Drivers License",
    "Request Residency Letter",
    "Request Salary Certificate",
    "Request Salary Transfer",
  ];
}