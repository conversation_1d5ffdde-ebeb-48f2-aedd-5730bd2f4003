import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/utils/style/colors.dart';

// ignore: must_be_immutable
class DmSansText extends StatelessWidget {
  String? text;
  double? fontSize;

  TextAlign? textAlign;
  Color? color;
  bool? underline;
  Color? decorationColor; // New parameter for underline color
  FontWeight? fontWeight;
  int? maxLines;
  double? fontheight;
  String? fontFamily;
  double? letterSpacing;
  TextOverflow? overflow;
  TextDecoration? textDecoration;

  DmSansText(
    this.text, {
    Key? key,
    this.fontSize,
    this.textAlign,
    this.color,
    this.fontWeight,
    this.underline = false,
    this.maxLines,
    this.fontheight,
    this.fontFamily,
    this.overflow,
    this.textDecoration,
    this.decorationColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text ?? '',
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      overflow: overflow ?? (maxLines != null ? TextOverflow.ellipsis : null),
      style: GoogleFonts.dmSans(
        letterSpacing: letterSpacing,
        height: fontheight ?? 1,
        fontWeight: fontWeight ?? FontWeight.w500,
        fontSize: fontSize ?? 16,
        color: color ?? AppColors.textColor,
        decoration:
            textDecoration ??
            (underline! ? TextDecoration.lineThrough : TextDecoration.none),
        decorationColor: decorationColor,
      ),
    );
  }
}

class OpenSansText extends StatelessWidget {
  String? text;
  double? fontSize;
  TextAlign? textAlign;
  Color? color;
  bool? underline;
  FontWeight? fontWeight;
  int? maxLines;
  double? fontheight;
  String? fontFamily;
  double? letterSpacing;
  TextOverflow? overflow;
  bool? softWrap;

  OpenSansText(
    this.text, {
    Key? key,
    this.fontSize,
    this.textAlign,
    this.color,
    this.fontWeight,
    this.underline = false,
    this.maxLines,
    this.fontheight,
    this.fontFamily,
    this.letterSpacing,
    this.overflow,
    this.softWrap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text ?? '',
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      overflow: overflow ?? (maxLines != null ? TextOverflow.ellipsis : null),
      softWrap: softWrap ?? true,
      style: GoogleFonts.openSans(
        letterSpacing: letterSpacing,
        height: fontheight ?? 1,
        fontWeight: fontWeight ?? FontWeight.w300,
        fontSize: fontSize ?? 16,
        color: color ?? const Color(0xff010101),
        decoration:
            underline! ? TextDecoration.lineThrough : TextDecoration.none,
      ),
    );
  }
}

class RobotoText extends StatelessWidget {
  final String? text;
  final double? fontSize;
  final TextAlign? textAlign;
  final Color? color;
  final FontWeight? fontWeight;
  final bool underline;
  final int? maxLines;
  final double? fontheight;
  final String? fontFamily;
  final double? letterSpacing;
  final TextOverflow? overflow;
  final bool? softWrap;

  const RobotoText(
    this.text, {
    Key? key,
    this.fontSize,
    this.textAlign,
    this.color,
    this.fontWeight,
    this.underline = false,
    this.maxLines,
    this.fontheight,
    this.fontFamily,
    this.letterSpacing,
    this.overflow,
    this.softWrap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text ?? '',
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      overflow: overflow ?? (maxLines != null ? TextOverflow.ellipsis : null),
      softWrap: softWrap ?? true,
      style: GoogleFonts.roboto(
        letterSpacing: letterSpacing,
        height: fontheight ?? 1,
        fontWeight:
            fontWeight ??
            FontWeight.w400, // Roboto's default normal weight is 400
        fontSize: fontSize ?? 16,
        color: color ?? const Color(0xff010101),
        decoration:
            underline ? TextDecoration.lineThrough : TextDecoration.none,
      ),
    );
  }
}

class InterText extends StatelessWidget {
  final String? text;
  final double? fontSize;
  final TextAlign? textAlign;
  final Color? color;
  final FontWeight? fontWeight;
  final bool underline;
  final int? maxLines;
  final double? fontheight;
  final String? fontFamily;
  final double? letterSpacing;
  final TextOverflow? overflow;
  final bool? softWrap;

  const InterText(
    this.text, {
    Key? key,
    this.fontSize,
    this.textAlign,
    this.color,
    this.fontWeight,
    this.underline = false,
    this.maxLines,
    this.fontheight,
    this.fontFamily,
    this.letterSpacing,
    this.overflow,
    this.softWrap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text ?? '',
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      overflow: overflow ?? (maxLines != null ? TextOverflow.ellipsis : null),
      softWrap: softWrap ?? true,
      style: GoogleFonts.inter(
        letterSpacing: letterSpacing,
        height: fontheight ?? 1,
        fontWeight:
            fontWeight ?? FontWeight.w400, // Inter's default weight is 400
        fontSize: fontSize ?? 16,
        color: color ?? AppColors.textColor,
        decoration:
            underline ? TextDecoration.lineThrough : TextDecoration.none,
      ),
    );
  }
}
