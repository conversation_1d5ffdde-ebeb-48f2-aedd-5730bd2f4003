# SEA App Information

## Summary
SEA App is a Flutter-based mobile application designed for educational institution management. It provides comprehensive features for parents, children, employee management, attendance tracking, leave management, and payroll systems. The app supports multiple platforms including Android, iOS, web, and desktop environments.

## Structure
- **lib/**: Core application code with screens, components, and business logic
- **android/**: Android platform-specific configuration and code
- **ios/**: iOS platform-specific configuration and code
- **web/**: Web platform configuration and assets
- **assets/**: Application resources including images and fonts
- **windows/**, **macos/**, **linux/**: Desktop platform configurations

## Language & Runtime
**Language**: Dart/Flutter
**Flutter Version**: 3.32.1 (stable channel)
**Dart Version**: 3.8.1
**SDK Constraints**: >=3.7.2 <4.0.0
**Build System**: Flutter build system
**Package Manager**: pub (Flutter/Dart)

## Dependencies
**Main Dependencies**:
- **UI/UX**: flutter_html, google_fonts, auto_size_text, lottie
- **State Management**: flutter_riverpod, flutter_bloc, riverpod
- **Networking**: dio, http, connectivity_plus
- **Storage**: shared_preferences, flutter_secure_storage, drift (SQLite)
- **Authentication**: google_sign_in, sign_in_with_apple, firebase_auth
- **Firebase Services**: firebase_core, firebase_messaging, firebase_analytics, firebase_crashlytics
- **Forms & Validation**: reactive_forms, pin_code_fields
- **Utilities**: intl, path_provider, permission_handler, geolocator

**Development Dependencies**:
- build_runner
- injectable_generator
- json_serializable
- drift_dev
- flutter_lints
- flutter_launcher_icons

## Build & Installation
```bash
# Install dependencies
flutter pub get

# Generate code (for JSON serialization, dependency injection, etc.)
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run the application in debug mode
flutter run

# Build release APK for Android
flutter build apk --release

# Build release IPA for iOS
flutter build ios --release

# Build for web
flutter build web
```

## Node.js Component
The repository also contains a small Node.js component, likely for automation or testing purposes:

**Configuration**: package.json
**Runtime**: Node.js
**Main Dependencies**: nodemon, openai
**Run Commands**:
```bash
npm run watch  # Run with file watching
npm run start  # Start the application
```

## Testing
No dedicated testing framework or test files were identified in the repository.

## Mobile Platform Support
**Android**:
- Min SDK: 23
- Target SDK: Determined by Flutter configuration
- Application ID: app.sea

**iOS**:
- Configured with standard Flutter iOS setup
- Uses Podfile for native dependencies

## Desktop Platform Support
The application includes configuration for Windows, macOS, and Linux platforms, allowing it to run as a desktop application across multiple operating systems.