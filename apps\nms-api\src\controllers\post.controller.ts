/* eslint-disable prettier/prettier */
import { PostService } from '../providers/services/post.service';
import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { ApiOperation, ApiBody, ApiResponse } from '@nestjs/swagger';
import { PostModel } from '../providers/models/post/post.model';
import { PostViewModelModel } from '../providers/models/post/postViewModel.model';
import { PostReactionPayloadModel } from '../providers/models/post/postReactionPayload.model';
import { PostViewModel } from '../providers/models/post/postView.model';
import { AddFeaturedPostPayloadVMModel } from '../providers/models/post/addFeaturedPostPayloadVM.model';
import { AddFlaggedPostPayloadVMModel } from '../providers/models/post/addFlaggedPostPayloadVM.model';
import { DeleteReportedPostViewModelModel } from '../providers/models/post/deleteReportedPostViewModel.model';
import { DeleteMultiplePostViewModelModel } from '../providers/models/post/deleteMultiplePostViewModel.model';
@Controller()
export class PostController {
  constructor(private readonly service: PostService) {}

  @Post('post/createPost')
  @ApiBody({ type: PostModel })
  @ApiResponse({ type: PostModel })
  public async CreatePost(@Body() body: any): Promise<PostModel> {
    // -- not used
    const response = await this.service.CreatePost(body);
    return response;
  }

  @Post('post/updatePost')
  @ApiBody({ type: PostModel })
  @ApiResponse({ type: PostModel })
  public async UpdatePost(@Body() body: any): Promise<PostModel> {
    // -- not used
    const response = await this.service.UpdatePost(body);
    return response;
  }

  @Post('post/deletePost')
  @ApiBody({ type: PostModel })
  @ApiResponse({ type: PostModel })
  public async DeletePost(@Body() body: any): Promise<PostModel> {
    // -- not used
    const response = await this.service.DeletePost(body);
    return response;
  }

  @Post('post/updatePostStatus')
  @ApiBody({ type: PostViewModelModel })
  @ApiResponse({ type: PostViewModelModel })
  public async UpdatePostStatus(
    @Body() body: any,
  ): Promise<PostViewModelModel> {
    // -- not used
    const response = await this.service.UpdatePostStatus(body);
    return response;
  }

  @Get('post/getPostById')
  @ApiResponse({ type: PostViewModelModel })
  public async GetPostById(@Query() query?: any): Promise<PostViewModelModel> {
    //  -- not used
    const { id } = query;
    const response = await this.service.GetPostById(id);
    return response;
  }

  @Post('post/addUpdatePostReaction')
  @ApiBody({ type: PostReactionPayloadModel })
  @ApiResponse({ type: PostReactionPayloadModel })
  public async AddUpdatePostReaction(
    @Body() body: any,
  ): Promise<PostReactionPayloadModel> {
    // -- not used
    const response = await this.service.AddUpdatePostReaction(body);
    return response;
  }

  @Get('post/getAllActivities')
  public async GetAllActivities(@Query() query?: any): Promise<any[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      selAcademicYearId,
      userId,
      userType,
      isActionRequired,
    } = query;
    const response = await this.service.GetAllActivities(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      selAcademicYearId,
      userId,
      userType,
      isActionRequired,
    );
    return response;
  }

  @Get('post/getAllBirthDays')
  @ApiResponse({ type: Array<PostModel> })
  public async GetAllBirthdays(@Query() query?: any): Promise<PostModel[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      selAcademicYearId,
      showAll,
    } = query;
    const response = await this.service.GetAllBirthdays(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      selAcademicYearId,
      showAll,
    );
    return response;
  }

  @Get('post/getAllPosts')
  @ApiResponse({ type: Array<PostViewModelModel> })
  public async GetAllPosts(
    @Query() query?: any,
  ): Promise<PostViewModelModel[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
      fromDate,
      toDate,
    } = query;
    const response = await this.service.GetAllPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
      fromDate,
      toDate,
    );
    return response;
  }

  @Get('post/getAllPostsForYou')
  @ApiResponse({ type: Array<PostViewModelModel> })
  public async GetAllPostsForYou(
    @Query() query?: any,
  ): Promise<PostViewModelModel[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
      fromDate,
      toDate,
    } = query;
    const response = await this.service.GetAllPostsForYou(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
      fromDate,
      toDate,
    );
    return response;
  }

  @Get('post/getMyJournalPostsWithCount')
  @ApiResponse({ type: Array<PostViewModelModel> })
  public async GetMyJournalPostsWithCount(
    @Query() query?: any,
  ): Promise<PostViewModelModel[]> {
    //  -- not used
    const { userId, userType, selAcademicYearId } = query;
    const response = await this.service.GetMyJournalPostsWithCount(
      userId,
      userType,
      selAcademicYearId,
    );
    return response;
  }

  @Get('post/getJournalMyPosts')
  @ApiResponse({ type: Array<PostViewModelModel> })
  public async GetJournalMyPosts(
    @Query() query?: any,
  ): Promise<PostViewModelModel[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    } = query;
    const response = await this.service.GetJournalMyPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    );
    return response;
  }

  @Get('post/getJournalTaggedPosts')
  @ApiResponse({ type: Array<PostViewModelModel> })
  public async GetJournalTaggedPosts(
    @Query() query?: any,
  ): Promise<PostViewModelModel[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    } = query;
    const response = await this.service.GetJournalTaggedPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    );
    return response;
  }

  @Get('post/getJournalUnderReviewPosts')
  @ApiResponse({ type: Array<PostViewModelModel> })
  public async GetJournalUnderReviewPosts(
    @Query() query?: any,
  ): Promise<PostViewModelModel[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    } = query;
    const response = await this.service.GetJournalUnderReviewPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    );
    return response;
  }

  @Get('post/getPostsWithReactionAndComments')
  @ApiResponse({ type: Array<PostViewModelModel> })
  public async GetPostsWithReactionAndComments(
    @Query() query?: any,
  ): Promise<PostViewModelModel[]> {
    //  -- not used
    const { postId, userId, userType } = query;
    const response = await this.service.GetPostsWithReactionAndComments(
      postId,
      userId,
      userType,
    );
    return response;
  }

  @Get('post/getAllTagProfilesByUserId')
  public async GetAllTagProfilesByUserId(@Query() query?: any): Promise<any[]> {
    //  -- not used
    const { search, isParent } = query;
    const response = await this.service.GetAllTagProfilesByUserId(
      search,
      isParent,
    );
    return response;
  }

  @Get('post/getReactionsByPostId')
  @ApiResponse({ type: Array<PostReactionPayloadModel> })
  public async GetReactionsByPostId(@Query() query?: any): Promise<any[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      postId,
      userId,
      userType,
    } = query;
    const response = await this.service.GetReactionsByPostId(
      fields,
      pageNumber,
      pageSize,
      orderBy,
      postId,
      userId,
      userType,
    );
    return response;
  }

  @Get('post/getPendingPosts')
  @ApiResponse({ type: Array<PostViewModelModel> })
  public async GetPendingPosts(
    @Query() query?: any,
  ): Promise<PostViewModelModel[]> {
    //  -- not used
    const { fields, pageNumber, pageSize, id, orderBy, selAcademicYearId } =
      query;
    const response = await this.service.GetPendingPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      selAcademicYearId,
    );
    return response;
  }

  @Get('post/getAllDraftedPosts')
  @ApiResponse({ type: Array<PostViewModelModel> })
  public async GetAllDraftedPosts(
    @Query() query?: any,
  ): Promise<PostViewModelModel[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    } = query;
    const response = await this.service.GetAllDraftedPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    );
    return response;
  }

  @Get('post/getNurseryClassSectionsByParentId')
  public async GetNurseryClassSectionsByParentId(
    @Query() query?: any,
  ): Promise<any[]> {
    //  -- not used
    const { parentId } = query;
    const response =
      await this.service.GetNurseryClassSectionsByParentId(parentId);
    return response;
  }

  @Post('post/addMultiplePostViews')
  @ApiBody({ type: PostViewModel })
  @ApiResponse({ type: PostViewModel })
  public async AddMultiplePostViews(@Body() body: any): Promise<PostViewModel> {
    // -- not used
    const response = await this.service.AddMultiplePostViews(body);
    return response;
  }

  @Get('post/getAllEventPosts')
  public async GetAllEventPosts(@Query() query?: any): Promise<any[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
      showAll,
    } = query;
    const response = await this.service.GetAllEventPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
      showAll,
    );
    return response;
  }

  @Get('post/getAllEventPostsForJournal')
  public async GetAllEventPostsForJournal(
    @Query() query?: any,
  ): Promise<any[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    } = query;
    const response = await this.service.GetAllEventPostsForJournal(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    );
    return response;
  }

  @Get('post/getAllScheduledPosts')
  public async GetAllScheduledPosts(@Query() query?: any): Promise<any[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    } = query;
    const response = await this.service.GetAllScheduledPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    );
    return response;
  }

  @Post('post/addOrUpdateFeaturedPost')
  @ApiBody({ type: AddFeaturedPostPayloadVMModel })
  @ApiResponse({ type: AddFeaturedPostPayloadVMModel })
  public async AddOrUpdateFeaturedPost(
    @Body() body: any,
  ): Promise<AddFeaturedPostPayloadVMModel> {
    // -- not used
    const response = await this.service.AddOrUpdateFeaturedPost(body);
    return response;
  }

  @Post('post/addOrRemovePostFlags')
  @ApiBody({ type: AddFlaggedPostPayloadVMModel })
  @ApiResponse({ type: AddFlaggedPostPayloadVMModel })
  public async AddOrRemovePostFlags(
    @Body() body: any,
  ): Promise<AddFlaggedPostPayloadVMModel> {
    // -- not used
    const response = await this.service.AddOrRemovePostFlags(body);
    return response;
  }

  @Get('post/getAllReportedPosts')
  public async GetAllReportedPosts(@Query() query?: any): Promise<any[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    } = query;
    const response = await this.service.GetAllReportedPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    );
    return response;
  }

  @Post('post/updateReportedPost')
  @ApiBody({ type: PostModel })
  @ApiResponse({ type: PostModel })
  public async UpdateReportedPost(@Body() body: any): Promise<PostModel> {
    // -- not used
    const response = await this.service.UpdateReportedPost(body);
    return response;
  }

  @Post('post/deleteReportedPosts')
  @ApiBody({ type: DeleteReportedPostViewModelModel })
  @ApiResponse({ type: DeleteReportedPostViewModelModel })
  public async DeleteReportedPosts(
    @Body() body: any,
  ): Promise<DeleteReportedPostViewModelModel> {
    // -- not used
    const response = await this.service.DeleteReportedPosts(body);
    return response;
  }

  @Get('post/getEditNeededPosts')
  public async GetEditNeededPosts(@Query() query?: any): Promise<any[]> {
    //  -- not used
    const {
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    } = query;
    const response = await this.service.GetEditNeededPosts(
      fields,
      pageNumber,
      pageSize,
      id,
      orderBy,
      userId,
      userType,
      selAcademicYearId,
    );
    return response;
  }

  @Post('post/deleteMultiplePost')
  @ApiBody({ type: DeleteMultiplePostViewModelModel })
  @ApiResponse({ type: DeleteMultiplePostViewModelModel })
  public async DeleteMultiplePost(
    @Body() body: any,
  ): Promise<DeleteMultiplePostViewModelModel> {
    // -- not used
    const response = await this.service.DeleteMultiplePost(body);
    return response;
  }

  @Post('post/publishMultiplePost')
  @ApiBody({ type: DeleteMultiplePostViewModelModel })
  @ApiResponse({ type: DeleteMultiplePostViewModelModel })
  public async PublishMultiplePost(
    @Body() body: any,
  ): Promise<DeleteMultiplePostViewModelModel> {
    // -- not used
    const response = await this.service.PublishMultiplePost(body);
    return response;
  }

  // -------------------------------------------------------------------
  //  POST  /post/generatePresignedUrlForDownload
  //  (thin wrapper around the gateway endpoint)
  // -------------------------------------------------------------------
  @Post('post/generatePresignedUrlForDownload')
  @ApiOperation({ summary: 'Return a presigned S3 URL for any media file.' })
  @ApiBody({
    description: 'File key as string',
    schema: { type: 'string' },
    examples: {
      example1: {
        value: 'c0d52637-d581-4687-b548-3b025a332a7f83b5a6c4-6508-4c9e-a8b3-93138a5860492iFAzt78uxJN5FflLv86NlhfUSf-desktop.jpg'
      }
    }
  })
  @ApiResponse({ status: 200, type: String })
  async generatePresignedUrl(
    @Body() fileKey: string,
  ): Promise<string> {
    // No Authorization header taken from the request ― token comes from DefaultHttpModules
    return this.service.generatePresignedUrl(fileKey);
  }
}
