

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/account/profile/components/ageCalcultor.dart';
import 'package:seawork/screens/account/profile/components/customProfilePictureWidget.dart';
import 'package:seawork/screens/account/profile/components/detailInfo.dart';
import 'package:seawork/screens/account/profile/components/personInfoWidget.dart';
import 'package:seawork/screens/account/profile/models/employeeInfoModel.dart';
import 'package:seawork/utils/style/colors.dart';

class PersonalInfoDetail extends StatelessWidget {
  final Item? firstItem;
  final String? selectedFieldDemographic;
  final Function(String) onFieldTapped;
  final bool isLoading;

  const PersonalInfoDetail({
    Key? key,
    required this.firstItem,
    required this.selectedFieldDemographic,
    required this.onFieldTapped,
    required this.isLoading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.viewColor),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          ProfileStackWidget(
            imageWidget: CustomJpgImage(
              imageName: "profilepic",
              boxFit: BoxFit.cover,
            ),
            showEditButton: true,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              DmSansText(
                '${firstItem?.firstName} ${firstItem?.lastName}',
                fontWeight: FontWeight.w700,
              ),
            ],
          ),
          Expanded(
            child: ListView(
              children: [
                _buildNameCard(),
                _buildDemographicInfoCard(),
                _buildBiographicalInfoCard(),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNameCard() {
    return Card(
      elevation: 4,
      // ignore: deprecated_member_use
      shadowColor: AppColors.shadowColor.withOpacity(0.25),
      child: CardSectionTwo(
        title: "Name",
        subtitle: "View and edit name details",
        showTrailing: true,
        trailingArrowType: "right_up",
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, bottom: 12),
            child: Divider(),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, bottom: 12, top: 12),
            child: Row(mainAxisAlignment: MainAxisAlignment.start,
              children: [
                OpenSansText(
                  "Global name",
                  textAlign: TextAlign.center,
                  fontSize: 12, 
                  fontWeight: FontWeight.w700,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),
          SelectableRow(
            label: "Title",
            value: "${firstItem?.salutation}",
            isSelected: selectedFieldDemographic == 'Title',
            onTap: onFieldTapped,
          ),
          SelectableRow(
            label: "First name",
            value: "${firstItem?.firstName}",
            isSelected: selectedFieldDemographic == 'First name',
            onTap: onFieldTapped,
          ),
          SelectableRow(
            label: "Middle name",
            value: "${firstItem?.middleName}",
            isSelected: selectedFieldDemographic == 'Middle name',
            onTap: onFieldTapped,
          ),
          SelectableRow(
            label: "Last name",
            value: "${firstItem?.lastName}",
            isSelected: selectedFieldDemographic == 'Last name',
            onTap: onFieldTapped,
          ),
          SelectableRow(
            label: "Full name as per passport",
            value: "${firstItem?.displayName}",
            isSelected: selectedFieldDemographic == 'Full name as per passport',
            onTap: onFieldTapped,
          ),
        ],
      ),
    );
  }

  Widget _buildDemographicInfoCard() {
    return Card(
      elevation: 4,
      // ignore: deprecated_member_use
      shadowColor:  AppColors.shadowColor.withOpacity(0.25),
      child: CardSectionTwo(
        title: "Demographic info",
        subtitle: "View and edit your demographic info",
        showTrailing: true,
        trailingArrowType: "right_up",
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, bottom: 12),
            child: Divider(),
          ),
          SelectableRow(
            label: "Country",
            value: "${firstItem?.country ?? "----"}",
            isSelected: selectedFieldDemographic == "Country",
            onTap: onFieldTapped,
          ),
          SelectableRow(
            label: "Religion",
            value: "${firstItem?.religion}",
            isSelected: selectedFieldDemographic == 'Religion',
            onTap: onFieldTapped,
          ),
          SelectableRow(
            label: "Gender",
            value: firstItem?.gender == 'M' 
                ? 'Male' 
                : firstItem?.gender == 'F' 
                    ? 'Female' 
                    : "${firstItem?.gender}",
            isSelected: selectedFieldDemographic == 'Gender',
            onTap: onFieldTapped,
          ),
          SelectableRow(
            label: "Material status",
            value: firstItem?.maritalStatus == 'M' 
                ? 'Married' 
                : firstItem?.maritalStatus == 'D' 
                    ? 'Divorced' 
                    : firstItem?.maritalStatus == 'W' 
                        ? 'Widowed' 
                        : firstItem?.maritalStatus == 'S' 
                            ? 'Single' 
                            : "${firstItem?.maritalStatus}",
            isSelected: selectedFieldDemographic == 'Religion',
            onTap: onFieldTapped,
          ),
          SelectableRow(
            label: "Highest level of education",
            value: "----",
            isSelected: selectedFieldDemographic == 'Highest level of education',
            onTap: onFieldTapped,
          ),
        ],
      ),
    );
  }

  Widget _buildBiographicalInfoCard() {
    return Card(
      elevation: 4,
      // ignore: deprecated_member_use
      shadowColor: AppColors.shadowColor.withOpacity(0.25),
      child: CardSectionTwo(
        title: "Biographical info",
        subtitle: "View and edit biographical info",
        showTrailing: true,
        trailingArrowType: "right_up",
        children: [
          const Padding(
            padding: EdgeInsets.only(left: 20, right: 20, bottom: 12),
            child: Divider(),
          ),
          SelectableRow(
            label: "Date of birth",
            value: firstItem?.dateOfBirth != null
                ? DateFormat('dd- MMM- yyyy').format(firstItem!.dateOfBirth)
                : "N/A",
            isSelected: selectedFieldDemographic == 'Date of birth',
            onTap: onFieldTapped,
          ),
          CalculateAge(
            dateOfBirth: firstItem?.dateOfBirth,
            isSelected: selectedFieldDemographic == 'Age',
            onTap: () {},
          ),
        ],
      ),
    );
  }
}