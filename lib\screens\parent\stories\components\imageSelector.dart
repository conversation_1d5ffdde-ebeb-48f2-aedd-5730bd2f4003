import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/image/renderImage.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/components/widget/customPainter.dart';
import 'package:seawork/screens/parent/stories/components/editPostScreen.dart';
import 'package:seawork/screens/parent/stories/components/storiesScreen.dart';
import 'dart:typed_data'; 
import 'package:seawork/utils/style/colors.dart';



class ImageSelectorWidget extends StatefulWidget {
    final bool hideRecents;
  const ImageSelectorWidget({Key? key, this.hideRecents = false}) : super(key: key);

  @override
  State<ImageSelectorWidget> createState() => _ImageSelectorWidgetState();
}

class _ImageSelectorWidgetState extends State<ImageSelectorWidget> {
 List<Uint8List> selectedImages = [];
  int _currentImageIndex = 0;
  Future<void> _pickImage([bool isEditing = false]) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.image,
      allowMultiple: !isEditing,
    );

    if (result != null && result.files.isNotEmpty) {
           setState(() {
        if (isEditing) {
          selectedImages[_currentImageIndex] = result.files.first.bytes!;
        } else {
          selectedImages.addAll(result.files.map((file) => file.bytes!));
        }
      });


    }
  }
  void _removeImage(int index) {
    setState(() {
      selectedImages.removeAt(index);
      if (_currentImageIndex >= selectedImages.length) {
        _currentImageIndex = selectedImages.length - 1;
      }
    });
  }
  Widget _buildImageContainer(Uint8List imageBytes, int index) {
    return Stack(
      children: [
        Container(
          width: selectedImages.length == 1 ? double.infinity : MediaQuery.of(context).size.width * 0.8,
          height: 160,
          margin: selectedImages.length == 1 ? null : const EdgeInsets.only(right: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            image: DecorationImage(
              image: MemoryImage(imageBytes),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 8,
          right:8,
          left: selectedImages.length == 1 ? 8 : null,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: () {
                  _currentImageIndex = index;
                  Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => EditPostScreen(   imageData: selectedImages[index], )),
                );
                },
                child: Container(
                  padding: const EdgeInsets.all(6),
                  
                  child: SvgImage24x24("assets/images/editIcon.svg", ),
                ),
              ),
              const SizedBox(height: 4),
              GestureDetector(
                onTap: () => _removeImage(index),
                child: Container(
                  padding: const EdgeInsets.all(6),
                
                  child: SvgImage24x24("assets/images/delete_Icon.svg", ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
              if (selectedImages.isEmpty)
         GestureDetector(
          onTap: _pickImage,
         child:  CustomPaint(
          painter: DashedBorderPainter(
            color: AppColors.lightGrey,
            strokeWidth: 1.5,
            dashWidth: 6.0,
            dashSpace: 4.0,
            borderRadius: 12.0,
          ),
          child: Container(
            width: double.infinity,
            height: 150,
            decoration: BoxDecoration(
              color: AppColors.lightBlue,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child:Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomSvgImage(imageName: "image_plus"),
                  const SizedBox(height: 8),
                  OpenSans400Large(
                    10,
                    "Select image from the album",
                    AppColors.lightGrey,
                  ),
                ],
              ),
            ),
          ),
        ),
         )
        else if (selectedImages.length == 1)
          _buildImageContainer(selectedImages.first, 0)
        else
          SizedBox(
            height: 160,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: selectedImages.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                  onTap: () => setState(() => _currentImageIndex = index),
                  child: _buildImageContainer(selectedImages[index], index),
                );
              },
            ),
          ),
       const SizedBox(height: 16),
            
        Row(
          children: [
               if (!widget.hideRecents) ...[
            OpenSans600Large(14, "Recents", AppColors.blackColor),
            const SizedBox(width: 4),
            SvgImage24x24('assets/images/Arrowleft.svg'),
               ],
            const Spacer(),
        GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => StoriesScreen()),
                );
              },
                   
              child: SvgImage24x24('assets/images/addPhotos.svg'),
            ),
            const SizedBox(width: 10),
            SvgImage24x24('assets/images/camera_icon.svg'),
          ],
        ),
      ],
    );
  }
}
