import 'package:flutter_test/flutter_test.dart';
import 'package:seawork/screens/parent/stories/model/storiesListModel.dart';
import 'package:seawork/models/mentioned_user.dart';

void main() {
  group('MentionedUser Model Tests', () {
    test('should create MentionedUser with student type', () {
      final user = MentionedUser(name: '<PERSON>', type: MentionUserType.student);
      expect(user.name, '<PERSON>');
      expect(user.type, MentionUserType.student);
    });

    test('should create MentionedUser with nursery type', () {
      final user = MentionedUser(name: 'Al Falah Nursery', type: MentionUserType.nursery);
      expect(user.name, 'Al Falah Nursery');
      expect(user.type, MentionUserType.nursery);
    });
  });

  group('Mentioned Users List Generation Tests', () {
    test('should generate list with nurseries for admin users', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 1,
          name: 'Admin User',
          role: 'Super Admin',
          assignedNurseries: [
            AssignedNursery(id: 1104, name: '<PERSON> Nursery'),
            AssignedNursery(id: 1105, name: '<PERSON> Nursery'),
          ],
        ),
      );

      final result = _getMentionedUsersList(story);
      expect(result.length, 2);
      expect(result[0].name, 'Al Falah Nursery');
      expect(result[0].type, MentionUserType.nursery);
      expect(result[1].name, 'Al Rahmaniya Nursery');
      expect(result[1].type, MentionUserType.nursery);
    });

    test('should generate list with students for parent users', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 784198774033027,
          name: 'Parent User',
          role: 'Father',
          students: [
            Student(id: 181, fullNameInEnglish: 'Niyaz'),
            Student(id: 182, fullNameInEnglish: 'Livin'),
          ],
        ),
      );

      final result = _getMentionedUsersList(story);
      expect(result.length, 2);
      expect(result[0].name, 'Niyaz');
      expect(result[0].type, MentionUserType.student);
      expect(result[1].name, 'Livin');
      expect(result[1].type, MentionUserType.student);
    });

    test('should filter out empty names', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 784198774033027,
          name: 'Parent User',
          role: 'Father',
          students: [
            Student(id: 181, fullNameInEnglish: ''),
            Student(id: 182, fullNameInEnglish: 'Livin'),
          ],
        ),
      );

      final result = _getMentionedUsersList(story);
      expect(result.length, 1);
      expect(result[0].name, 'Livin');
      expect(result[0].type, MentionUserType.student);
    });
  });

  group('Mentioned Users Functionality Tests', () {
    test('should return empty string when createdBy is null', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: null,
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, '');
    });

    test('should show single assigned nursery for admin users', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 1,
          name: 'Admin User',
          role: 'Super Admin',
          assignedNurseries: [
            AssignedNursery(
              id: 1104,
              name: 'Al Falah Nursery',
              nameInArabic: 'حضانة الفلاح',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Al Falah Nursery');
    });

    test('should show first nursery with ellipsis for multiple assigned nurseries', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 1,
          name: 'Admin User',
          role: 'Super Admin',
          assignedNurseries: [
            AssignedNursery(
              id: 1104,
              name: 'Al Falah Nursery',
              nameInArabic: 'حضانة الفلاح',
            ),
            AssignedNursery(
              id: 1105,
              name: 'Al Rahmaniya Nursery',
              nameInArabic: 'حضانة الرحمانية',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Al Falah Nursery...');
    });

    test('should show single student for parent users', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 784198774033027,
          name: 'Asad Afreen',
          role: 'Father',
          students: [
            Student(
              id: 181,
              fullNameInEnglish: 'Niyaz',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Niyaz');
    });

    test('should show first student with ellipsis for multiple students', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 784198774033027,
          name: 'Asad Afreen',
          role: 'Father',
          students: [
            Student(
              id: 181,
              fullNameInEnglish: 'Niyaz',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
            Student(
              id: 182,
              fullNameInEnglish: 'Livin',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Niyaz...');
    });

    test('should prioritize assigned nurseries over students', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 1,
          name: 'Admin User',
          role: 'Super Admin',
          assignedNurseries: [
            AssignedNursery(
              id: 1104,
              name: 'Al Falah Nursery',
              nameInArabic: 'حضانة الفلاح',
            ),
          ],
          students: [
            Student(
              id: 181,
              fullNameInEnglish: 'Niyaz',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Al Falah Nursery');
    });

    test('should return empty string when no nurseries or students', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 1,
          name: 'User',
          role: 'Teacher',
          assignedNurseries: [],
          students: [],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, '');
    });

    test('should handle empty student names', () {
      final story = StoriesListModel(
        id: 1,
        postBody: 'Test post',
        createdBy: CreatedBy(
          id: 784198774033027,
          name: 'Parent User',
          role: 'Father',
          students: [
            Student(
              id: 181,
              fullNameInEnglish: '',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
            Student(
              id: 182,
              fullNameInEnglish: 'Livin',
              fullNameInArabic: 'ادم ساجد ساجد فالابيل بيديكايل',
            ),
          ],
        ),
      );
      
      final result = _buildMentionedUsers(story);
      expect(result, ' • Livin');
    });
  });
}

// Helper function extracted from the main implementation for testing
String _buildMentionedUsers(StoriesListModel story) {
  if (story.createdBy == null) return '';
  
  final createdBy = story.createdBy!;
  
  // Check if there are assigned nurseries (for admin/staff users)
  if (createdBy.assignedNurseries != null && createdBy.assignedNurseries!.isNotEmpty) {
    if (createdBy.assignedNurseries!.length == 1) {
      return ' • ${createdBy.assignedNurseries!.first.name ?? ''}';
    } else {
      return ' • ${createdBy.assignedNurseries!.first.name ?? ''}...';
    }
  }
  
  // Check if there are students (for parent users)
  if (createdBy.students != null && createdBy.students!.isNotEmpty) {
    final studentNames = createdBy.students!
        .map((student) => student.fullNameInEnglish ?? '')
        .where((name) => name.isNotEmpty)
        .toList();
    
    if (studentNames.isEmpty) return '';
    
    if (studentNames.length == 1) {
      return ' • ${studentNames.first}';
    } else {
      return ' • ${studentNames.first}...';
    }
  }
  
  return '';
}

// Helper function for testing mentioned users list generation
List<MentionedUser> _getMentionedUsersList(StoriesListModel story) {
  final List<MentionedUser> users = [];

  if (story.createdBy == null) return users;
  final createdBy = story.createdBy!;

  // Add assigned nurseries (for admin/staff users)
  if (createdBy.assignedNurseries != null && createdBy.assignedNurseries!.isNotEmpty) {
    for (final nursery in createdBy.assignedNurseries!) {
      if (nursery.name != null && nursery.name!.isNotEmpty) {
        users.add(MentionedUser(
          name: nursery.name!,
          type: MentionUserType.nursery,
        ));
      }
    }
  }

  // Add students (for parent users)
  if (createdBy.students != null && createdBy.students!.isNotEmpty) {
    for (final student in createdBy.students!) {
      if (student.fullNameInEnglish != null && student.fullNameInEnglish!.isNotEmpty) {
        users.add(MentionedUser(
          name: student.fullNameInEnglish!,
          type: MentionUserType.student,
        ));
      }
    }
  }

  return users;
}
