import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PostController } from '../controllers/post.controller';
import { DefaultHttpModules } from '../moduleInit';
import { PostService } from '../providers/services/post.service';
@Module({
  // -- not used
  imports: [DefaultHttpModules(), ConfigModule],
  controllers: [PostController],
  providers: [PostService],
})
export class PostModule {}
