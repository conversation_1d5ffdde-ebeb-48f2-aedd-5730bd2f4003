/* eslint-disable prettier/prettier */
import { Injectable } from '@nestjs/common';
import { HttpService } from 'packages/http';
import { ConfigService } from '@nestjs/config';
import { PostModel } from '../models/post/post.model';
import { PostViewModelModel } from '../models/post/postViewModel.model';
import { PostReactionPayloadModel } from '../models/post/postReactionPayload.model';
import { PostViewModel } from '../models/post/postView.model';
import { AddFeaturedPostPayloadVMModel } from '../models/post/addFeaturedPostPayloadVM.model';
import { AddFlaggedPostPayloadVMModel } from '../models/post/addFlaggedPostPayloadVM.model';
import { DeleteReportedPostViewModelModel } from '../models/post/deleteReportedPostViewModel.model';
import { DeleteMultiplePostViewModelModel } from '../models/post/deleteMultiplePostViewModel.model';
@Injectable()
export class PostService {
  constructor(
    private readonly http: HttpService,
    private readonly config: ConfigService,
  ) {}

  public async CreatePost(model: PostModel): Promise<PostModel> {
    // -- not used
    const response = await this.http.post<PostModel>(`post/CreatePost`, model);
    return response;
  }

  public async UpdatePost(model: PostModel): Promise<PostModel> {
    // -- not used
    const response = await this.http.post<PostModel>(`post/UpdatePost`, model);
    return response;
  }

  public async DeletePost(model: PostModel): Promise<PostModel> {
    // -- not used
    const response = await this.http.post<PostModel>(`post/DeletePost`, model);
    return response;
  }

  public async UpdatePostStatus(
    model: PostViewModelModel,
  ): Promise<PostViewModelModel> {
    // -- not used
    const response = await this.http.post<PostViewModelModel>(
      `post/UpdatePostStatus`,
      model,
    );
    return response;
  }

  public async GetPostById(id?: number): Promise<PostViewModelModel> {
    // -- not used
    const response = await this.http.get<PostViewModelModel>(
      `post/GetPostById`,
      { Id: id },
      false,
    );
    return response;
  }

  public async AddUpdatePostReaction(
    model: PostReactionPayloadModel,
  ): Promise<PostReactionPayloadModel> {
    // -- not used
    const response = await this.http.post<PostReactionPayloadModel>(
      `post/AddUpdatePostReaction`,
      model,
    );
    return response;
  }

  public async GetAllActivities(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    selAcademicYearId?: number,
    userId?: number,
    userType?: number,
    isActionRequired?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `post/GetAllActivities`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        SelAcademicYearId: selAcademicYearId,
        userId: userId,
        userType: userType,
        IsActionRequired: isActionRequired,
      },
      true,
    );
    return response;
  }

  public async GetAllBirthdays(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    selAcademicYearId?: number,
    showAll?: boolean,
  ): Promise<PostModel[]> {
    // -- not used
    const response = await this.http.get<PostModel[]>(
      `post/GetAllBirthDays`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        SelAcademicYearId: selAcademicYearId,
        ShowAll: showAll,
      },
      true,
    );
    return response;
  }

  public async GetAllPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
    fromDate?: string,
    toDate?: string,
  ): Promise<PostViewModelModel[]> {
    // -- not used
    const response = await this.http.get<PostViewModelModel[]>(
      `post/GetAllPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
        FromDate: fromDate,
        ToDate: toDate,
      },
      true,
    );
    return response;
  }

  public async GetAllPostsForYou(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
    fromDate?: string,
    toDate?: string,
  ): Promise<PostViewModelModel[]> {
    // -- not used
    const response = await this.http.get<PostViewModelModel[]>(
      `post/GetAllPostsForYou`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
        FromDate: fromDate,
        ToDate: toDate,
      },
      true,
    );
    return response;
  }

  public async GetMyJournalPostsWithCount(
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
  ): Promise<PostViewModelModel[]> {
    // -- not used
    const response = await this.http.get<PostViewModelModel[]>(
      `post/GetMyJournalPostsWithCount`,
      {
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async GetJournalMyPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
  ): Promise<PostViewModelModel[]> {
    // -- not used
    const response = await this.http.get<PostViewModelModel[]>(
      `post/GetJournalMyPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async GetJournalTaggedPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
  ): Promise<PostViewModelModel[]> {
    // -- not used
    const response = await this.http.get<PostViewModelModel[]>(
      `post/GetJournalTaggedPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async GetJournalUnderReviewPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
  ): Promise<PostViewModelModel[]> {
    // -- not used
    const response = await this.http.get<PostViewModelModel[]>(
      `post/GetJournalUnderReviewPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async GetPostsWithReactionAndComments(
    postId?: number,
    userId?: number,
    userType?: number,
  ): Promise<PostViewModelModel[]> {
    // -- not used
    const response = await this.http.get<PostViewModelModel[]>(
      `post/GetPostsWithReactionAndComments`,
      { PostId: postId, userId: userId, userType: userType },
      true,
    );
    return response;
  }

  public async GetAllTagProfilesByUserId(
    search?: string,
    isParent?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `post/GetAllTagProfilesByUserId`,
      { search: search, IsParent: isParent },
      true,
    );
    return response;
  }

  public async GetReactionsByPostId(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    orderBy?: string,
    postId?: number,
    userId?: number,
    userType?: number,
  ): Promise<any[]> {
    // -- not used
    const response = await this.http.get<any[]>(
      `post/GetReactionsByPostId`,
      {
        PageNumber: pageNumber,
        PageSize: pageSize,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        PostId: postId,
      },
      true,
    );
    return response;
  }

  public async GetPendingPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    selAcademicYearId?: number,
  ): Promise<PostViewModelModel[]> {
    // -- not used
    const response = await this.http.get<PostViewModelModel[]>(
      `post/GetPendingPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async GetAllDraftedPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
  ): Promise<PostViewModelModel[]> {
    // -- not used
    const response = await this.http.get<PostViewModelModel[]>(
      `post/GetAllDraftedPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async GetNurseryClassSectionsByParentId(
    parentId?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `post/GetNurseryClassSectionsByParentId`,
      { ParentId: parentId },
      true,
    );
    return response;
  }

  public async AddMultiplePostViews(
    model: PostViewModel,
  ): Promise<PostViewModel> {
    // -- not used
    const response = await this.http.post<PostViewModel>(
      `post/AddMultiplePostViews`,
      model,
    );
    return response;
  }

  public async GetAllEventPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
    showAll?: boolean,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `post/GetAllEventPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
        ShowAll: showAll,
      },
      true,
    );
    return response;
  }

  public async GetAllEventPostsForJournal(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `post/GetAllEventPostsForJournal`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async GetAllScheduledPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `post/GetAllScheduledPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async AddOrUpdateFeaturedPost(
    model: AddFeaturedPostPayloadVMModel,
  ): Promise<AddFeaturedPostPayloadVMModel> {
    // -- not used
    const response = await this.http.post<AddFeaturedPostPayloadVMModel>(
      `post/AddOrUpdateFeaturedPost`,
      model,
    );
    return response;
  }

  public async AddOrRemovePostFlags(
    model: AddFlaggedPostPayloadVMModel,
  ): Promise<AddFlaggedPostPayloadVMModel> {
    // -- not used
    const response = await this.http.post<AddFlaggedPostPayloadVMModel>(
      `post/AddOrRemovePostFlags`,
      model,
    );
    return response;
  }

  public async GetAllReportedPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `post/GetAllReportedPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async UpdateReportedPost(model: PostModel): Promise<PostModel> {
    // -- not used
    const response = await this.http.post<PostModel>(
      `post/UpdateReportedPost`,
      model,
    );
    return response;
  }

  public async DeleteReportedPosts(
    model: DeleteReportedPostViewModelModel,
  ): Promise<DeleteReportedPostViewModelModel> {
    // -- not used
    const response = await this.http.post<DeleteReportedPostViewModelModel>(
      `post/DeleteReportedPosts`,
      model,
    );
    return response;
  }

  public async GetEditNeededPosts(
    fields?: string,
    pageNumber?: number,
    pageSize?: number,
    id?: number,
    orderBy?: string,
    userId?: number,
    userType?: number,
    selAcademicYearId?: number,
  ): Promise<any> {
    // -- not used
    const response = await this.http.get(
      `post/GetEditNeededPosts`,
      {
        Fields: fields,
        PageNumber: pageNumber,
        PageSize: pageSize,
        Id: id,
        OrderBy: orderBy,
        userId: userId,
        userType: userType,
        SelAcademicYearId: selAcademicYearId,
      },
      true,
    );
    return response;
  }

  public async DeleteMultiplePost(
    model: DeleteMultiplePostViewModelModel,
  ): Promise<DeleteMultiplePostViewModelModel> {
    // -- not used
    const response = await this.http.post<DeleteMultiplePostViewModelModel>(
      `post/DeleteMultiplePost`,
      model,
    );
    return response;
  }

  public async PublishMultiplePost(
    model: DeleteMultiplePostViewModelModel,
  ): Promise<DeleteMultiplePostViewModelModel> {
    // -- not used
    const response = await this.http.post<DeleteMultiplePostViewModelModel>(
      `post/PublishMultiplePost`,
      model,
    );
    return response;
  }

  // -------------------------------------------------------------------
  //  generatePresignedUrl  (follows the style of CreatePost above)
  // -------------------------------------------------------------------
  async generatePresignedUrl(fileKey: string): Promise<string> {
    const url = `upload/GeneratePresignedUrlForDownload`;

    // The HttpModule instance created in DefaultHttpModules already
    //  1) carries the bearer token,
    //  2) understands absolute URLs (allowAbsoluteUrls = true).
    const data = await this.http.post<string>(url, `${fileKey}`, {
      params: {
        IsThumbNail: false,
        IsCertificate: false,
      },
    });

    return data;  
     // plain presigned URL
  }
}
